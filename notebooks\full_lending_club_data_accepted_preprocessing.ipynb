{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3e6092f5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import pathlib as pl\n", "import warnings\n", "# warnings.filterwarnings('ignore')\n", "# Set display options for better readability\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)"]}, {"cell_type": "code", "execution_count": 2, "id": "6ecd80e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('64bit', 'WindowsPE')\n"]}], "source": ["import platform\n", "print(platform.architecture())"]}, {"cell_type": "code", "execution_count": 2, "id": "d1026864", "metadata": {}, "outputs": [], "source": ["# If you're running this notebook using the .venv python, pwd = /notebooks\n", "DATA_PATH = \"..\\\\data\\\\Full Lending Club loan data\\\\accepted_2007_to_2018q4\\\\accepted_2007_to_2018Q4.csv\"\n", "# If you're running the jupyter-lab server from the root project dict and connecting to its Kernal, pwd = /\n", "# DATA_PATH = pl.Path.cwd() / \"data\" / \"Full Lending Club loan data\" / \"accepted_2007_to_2018q4\" / \"accepted_2007_to_2018Q4.csv\""]}, {"cell_type": "code", "execution_count": 8, "id": "25cdfb97", "metadata": {}, "outputs": [], "source": ["import dask.dataframe as dd"]}, {"cell_type": "code", "execution_count": 9, "id": "be394bb7", "metadata": {}, "outputs": [], "source": ["df = dd.read_csv(DATA_PATH)"]}, {"cell_type": "code", "execution_count": 10, "id": "8f356c52", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\dataframe\\io\\csv.py:77: DtypeWarning: Columns (19,59) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = reader(bio, **kwargs)\n"]}, {"ename": "ValueError", "evalue": "Mismatched dtypes found in `pd.read_csv`/`pd.read_table`.\n\n+---------------------------+--------+----------+\n| Column                    | Found  | Expected |\n+---------------------------+--------+----------+\n| debt_settlement_flag_date | object | float64  |\n| desc                      | object | float64  |\n| hardship_end_date         | object | float64  |\n| hardship_loan_status      | object | float64  |\n| hardship_reason           | object | float64  |\n| hardship_start_date       | object | float64  |\n| hardship_status           | object | float64  |\n| hardship_type             | object | float64  |\n| payment_plan_start_date   | object | float64  |\n| settlement_date           | object | float64  |\n| settlement_status         | object | float64  |\n+---------------------------+--------+----------+\n\nThe following columns also raised exceptions on conversion:\n\n- debt_settlement_flag_date\n  ValueError(\"could not convert string to float: 'Nov-2017'\")\n- desc\n  ValueError(\"could not convert string to float: 'We knew that using our credit cards to finance an adoption would squeeze us, but then medical and other unexpected expenses made the situation almost impossible. We are a stable family in a stable community. We just need to break a cycle of debt that is getting worse.'\")\n- hardship_end_date\n  ValueError(\"could not convert string to float: 'Dec-2017'\")\n- hardship_loan_status\n  ValueError(\"could not convert string to float: 'Late (16-30 days)'\")\n- hardship_reason\n  ValueError(\"could not convert string to float: 'NATURAL_DISASTER'\")\n- hardship_start_date\n  ValueError(\"could not convert string to float: 'Sep-2017'\")\n- hardship_status\n  ValueError(\"could not convert string to float: 'BROKEN'\")\n- hardship_type\n  ValueError(\"could not convert string to float: 'INTEREST ONLY-3 MONTHS DEFERRAL'\")\n- payment_plan_start_date\n  ValueError(\"could not convert string to float: 'Oct-2017'\")\n- settlement_date\n  ValueError(\"could not convert string to float: 'Sep-2017'\")\n- settlement_status\n  ValueError(\"could not convert string to float: 'COMPLETE'\")\n\nUsually this is due to dask's dtype inference failing, and\n*may* be fixed by specifying dtypes manually by adding:\n\ndtype={'debt_settlement_flag_date': 'object',\n       'desc': 'object',\n       'hardship_end_date': 'object',\n       'hardship_loan_status': 'object',\n       'hardship_reason': 'object',\n       'hardship_start_date': 'object',\n       'hardship_status': 'object',\n       'hardship_type': 'object',\n       'payment_plan_start_date': 'object',\n       'settlement_date': 'object',\n       'settlement_status': 'object'}\n\nto the call to `read_csv`/`read_table`.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhead\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\dataframe\\dask_expr\\_collection.py:692\u001b[0m, in \u001b[0;36mFrameBase.head\u001b[1;34m(self, n, npartitions, compute)\u001b[0m\n\u001b[0;32m    690\u001b[0m out \u001b[38;5;241m=\u001b[39m new_collection(expr\u001b[38;5;241m.\u001b[39mHead(\u001b[38;5;28mself\u001b[39m, n\u001b[38;5;241m=\u001b[39mn, npartitions\u001b[38;5;241m=\u001b[39mnpartitions))\n\u001b[0;32m    691\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m compute:\n\u001b[1;32m--> 692\u001b[0m     out \u001b[38;5;241m=\u001b[39m \u001b[43mout\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompute\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    693\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m out\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\base.py:373\u001b[0m, in \u001b[0;36mDaskMethodsMixin.compute\u001b[1;34m(self, **kwargs)\u001b[0m\n\u001b[0;32m    349\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mcompute\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m    350\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Compute this dask collection\u001b[39;00m\n\u001b[0;32m    351\u001b[0m \n\u001b[0;32m    352\u001b[0m \u001b[38;5;124;03m    This turns a lazy Dask collection into its in-memory equivalent.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    371\u001b[0m \u001b[38;5;124;03m    dask.compute\u001b[39;00m\n\u001b[0;32m    372\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 373\u001b[0m     (result,) \u001b[38;5;241m=\u001b[39m compute(\u001b[38;5;28mself\u001b[39m, traverse\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    374\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\base.py:681\u001b[0m, in \u001b[0;36mcompute\u001b[1;34m(traverse, optimize_graph, scheduler, get, *args, **kwargs)\u001b[0m\n\u001b[0;32m    678\u001b[0m     expr \u001b[38;5;241m=\u001b[39m expr\u001b[38;5;241m.\u001b[39moptimize()\n\u001b[0;32m    679\u001b[0m     keys \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(flatten(expr\u001b[38;5;241m.\u001b[39m__dask_keys__()))\n\u001b[1;32m--> 681\u001b[0m     results \u001b[38;5;241m=\u001b[39m schedule(expr, keys, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    683\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m repack(results)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\dataframe\\io\\csv.py:351\u001b[0m, in \u001b[0;36m_read_csv\u001b[1;34m(block, part, columns, reader, header, dtypes, head, colname, full_columns, enforce, kwargs, blocksize)\u001b[0m\n\u001b[0;32m    348\u001b[0m         rest_kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124musecols\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m _columns\n\u001b[0;32m    350\u001b[0m \u001b[38;5;66;03m# Call `pandas_read_text`\u001b[39;00m\n\u001b[1;32m--> 351\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mpandas_read_text\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    352\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreader\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    353\u001b[0m \u001b[43m    \u001b[49m\u001b[43mblock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    354\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheader\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    355\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrest_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    356\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtypes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    357\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_columns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    358\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwrite_header\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    359\u001b[0m \u001b[43m    \u001b[49m\u001b[43menforce\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    360\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpath_info\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    361\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    362\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m project_after_read:\n\u001b[0;32m    363\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m df[columns]\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\dataframe\\io\\csv.py:79\u001b[0m, in \u001b[0;36mpandas_read_text\u001b[1;34m(reader, b, header, kwargs, dtypes, columns, write_header, enforce, path)\u001b[0m\n\u001b[0;32m     77\u001b[0m df \u001b[38;5;241m=\u001b[39m reader(bio, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m     78\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dtypes:\n\u001b[1;32m---> 79\u001b[0m     \u001b[43mcoerce_dtypes\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtypes\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     81\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m enforce \u001b[38;5;129;01mand\u001b[39;00m columns \u001b[38;5;129;01mand\u001b[39;00m (\u001b[38;5;28mlist\u001b[39m(df\u001b[38;5;241m.\u001b[39mcolumns) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mlist\u001b[39m(columns)):\n\u001b[0;32m     82\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mColumns do not match\u001b[39m\u001b[38;5;124m\"\u001b[39m, df\u001b[38;5;241m.\u001b[39mcolumns, columns)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\dask\\dataframe\\io\\csv.py:180\u001b[0m, in \u001b[0;36mcoerce_dtypes\u001b[1;34m(df, dtypes)\u001b[0m\n\u001b[0;32m    176\u001b[0m rule \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m61\u001b[39m)\n\u001b[0;32m    177\u001b[0m msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMismatched dtypes found in `pd.read_csv`/`pd.read_table`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m (\n\u001b[0;32m    178\u001b[0m     rule\u001b[38;5;241m.\u001b[39mjoin(\u001b[38;5;28mfilter\u001b[39m(\u001b[38;5;28;01mNone\u001b[39;00m, [dtype_msg, date_msg]))\n\u001b[0;32m    179\u001b[0m )\n\u001b[1;32m--> 180\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(msg)\n", "\u001b[1;31mValueError\u001b[0m: Mismatched dtypes found in `pd.read_csv`/`pd.read_table`.\n\n+---------------------------+--------+----------+\n| Column                    | Found  | Expected |\n+---------------------------+--------+----------+\n| debt_settlement_flag_date | object | float64  |\n| desc                      | object | float64  |\n| hardship_end_date         | object | float64  |\n| hardship_loan_status      | object | float64  |\n| hardship_reason           | object | float64  |\n| hardship_start_date       | object | float64  |\n| hardship_status           | object | float64  |\n| hardship_type             | object | float64  |\n| payment_plan_start_date   | object | float64  |\n| settlement_date           | object | float64  |\n| settlement_status         | object | float64  |\n+---------------------------+--------+----------+\n\nThe following columns also raised exceptions on conversion:\n\n- debt_settlement_flag_date\n  ValueError(\"could not convert string to float: 'Nov-2017'\")\n- desc\n  ValueError(\"could not convert string to float: 'We knew that using our credit cards to finance an adoption would squeeze us, but then medical and other unexpected expenses made the situation almost impossible. We are a stable family in a stable community. We just need to break a cycle of debt that is getting worse.'\")\n- hardship_end_date\n  ValueError(\"could not convert string to float: 'Dec-2017'\")\n- hardship_loan_status\n  ValueError(\"could not convert string to float: 'Late (16-30 days)'\")\n- hardship_reason\n  ValueError(\"could not convert string to float: 'NATURAL_DISASTER'\")\n- hardship_start_date\n  ValueError(\"could not convert string to float: 'Sep-2017'\")\n- hardship_status\n  ValueError(\"could not convert string to float: 'BROKEN'\")\n- hardship_type\n  ValueError(\"could not convert string to float: 'INTEREST ONLY-3 MONTHS DEFERRAL'\")\n- payment_plan_start_date\n  ValueError(\"could not convert string to float: 'Oct-2017'\")\n- settlement_date\n  ValueError(\"could not convert string to float: 'Sep-2017'\")\n- settlement_status\n  ValueError(\"could not convert string to float: 'COMPLETE'\")\n\nUsually this is due to dask's dtype inference failing, and\n*may* be fixed by specifying dtypes manually by adding:\n\ndtype={'debt_settlement_flag_date': 'object',\n       'desc': 'object',\n       'hardship_end_date': 'object',\n       'hardship_loan_status': 'object',\n       'hardship_reason': 'object',\n       'hardship_start_date': 'object',\n       'hardship_status': 'object',\n       'hardship_type': 'object',\n       'payment_plan_start_date': 'object',\n       'settlement_date': 'object',\n       'settlement_status': 'object'}\n\nto the call to `read_csv`/`read_table`."]}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "7d177aa9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19,59) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,59,118) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,118) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,118,129,130,131,134,135,136,139,145,146,147) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,129,130,131,134,135,136,139,145,146,147) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,118) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,59) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,59,118,129,130,131,134,135,136,139,145,146,147) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,129,130,131,134,135,136,139,145,146,147) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (129,130,131,134,135,136,139) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,49,59,118,129,130,131,134,135,136,139) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,49,59) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (129,130,131,134,135,136,139) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,49,59,129,130,131,134,135,136,139) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19,59,118) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,118) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20512\\2190327875.py:4: DtypeWarning: Columns (0,19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.concat(chunks, ignore_index=True)\n"]}], "source": ["chunk_size = 100_000  # adjust based on your system\n", "chunks = pd.read_csv(DATA_PATH, chunksize=chunk_size)\n", "\n", "df = pd.concat(chunks, ignore_index=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2dc25f48", "metadata": {}, "outputs": [], "source": ["%%time\n", "\n", "df = pd.read_csv(DATA_PATH)\n", "# df = pd.read_csv(DATA_PATH, nrows=10)\n", "# df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "ef08a677", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>member_id</th>\n", "      <th>loan_amnt</th>\n", "      <th>funded_amnt</th>\n", "      <th>funded_amnt_inv</th>\n", "      <th>term</th>\n", "      <th>int_rate</th>\n", "      <th>installment</th>\n", "      <th>grade</th>\n", "      <th>sub_grade</th>\n", "      <th>emp_title</th>\n", "      <th>emp_length</th>\n", "      <th>home_ownership</th>\n", "      <th>annual_inc</th>\n", "      <th>verification_status</th>\n", "      <th>issue_d</th>\n", "      <th>loan_status</th>\n", "      <th>pymnt_plan</th>\n", "      <th>url</th>\n", "      <th>desc</th>\n", "      <th>purpose</th>\n", "      <th>title</th>\n", "      <th>zip_code</th>\n", "      <th>addr_state</th>\n", "      <th>dti</th>\n", "      <th>delinq_2yrs</th>\n", "      <th>earliest_cr_line</th>\n", "      <th>fico_range_low</th>\n", "      <th>fico_range_high</th>\n", "      <th>inq_last_6mths</th>\n", "      <th>mths_since_last_delinq</th>\n", "      <th>mths_since_last_record</th>\n", "      <th>open_acc</th>\n", "      <th>pub_rec</th>\n", "      <th>revol_bal</th>\n", "      <th>revol_util</th>\n", "      <th>total_acc</th>\n", "      <th>initial_list_status</th>\n", "      <th>out_prncp</th>\n", "      <th>out_prncp_inv</th>\n", "      <th>total_pymnt</th>\n", "      <th>total_pymnt_inv</th>\n", "      <th>total_rec_prncp</th>\n", "      <th>total_rec_int</th>\n", "      <th>total_rec_late_fee</th>\n", "      <th>recoveries</th>\n", "      <th>collection_recovery_fee</th>\n", "      <th>last_pymnt_d</th>\n", "      <th>last_pymnt_amnt</th>\n", "      <th>next_pymnt_d</th>\n", "      <th>last_credit_pull_d</th>\n", "      <th>last_fico_range_high</th>\n", "      <th>last_fico_range_low</th>\n", "      <th>collections_12_mths_ex_med</th>\n", "      <th>mths_since_last_major_derog</th>\n", "      <th>policy_code</th>\n", "      <th>application_type</th>\n", "      <th>annual_inc_joint</th>\n", "      <th>dti_joint</th>\n", "      <th>verification_status_joint</th>\n", "      <th>acc_now_delinq</th>\n", "      <th>tot_coll_amt</th>\n", "      <th>tot_cur_bal</th>\n", "      <th>open_acc_6m</th>\n", "      <th>open_act_il</th>\n", "      <th>open_il_12m</th>\n", "      <th>open_il_24m</th>\n", "      <th>mths_since_rcnt_il</th>\n", "      <th>total_bal_il</th>\n", "      <th>il_util</th>\n", "      <th>open_rv_12m</th>\n", "      <th>open_rv_24m</th>\n", "      <th>max_bal_bc</th>\n", "      <th>all_util</th>\n", "      <th>total_rev_hi_lim</th>\n", "      <th>inq_fi</th>\n", "      <th>total_cu_tl</th>\n", "      <th>inq_last_12m</th>\n", "      <th>acc_open_past_24mths</th>\n", "      <th>avg_cur_bal</th>\n", "      <th>bc_open_to_buy</th>\n", "      <th>bc_util</th>\n", "      <th>chargeoff_within_12_mths</th>\n", "      <th>delinq_amnt</th>\n", "      <th>mo_sin_old_il_acct</th>\n", "      <th>mo_sin_old_rev_tl_op</th>\n", "      <th>mo_sin_rcnt_rev_tl_op</th>\n", "      <th>mo_sin_rcnt_tl</th>\n", "      <th>mort_acc</th>\n", "      <th>mths_since_recent_bc</th>\n", "      <th>mths_since_recent_bc_dlq</th>\n", "      <th>mths_since_recent_inq</th>\n", "      <th>mths_since_recent_revol_delinq</th>\n", "      <th>num_accts_ever_120_pd</th>\n", "      <th>num_actv_bc_tl</th>\n", "      <th>num_actv_rev_tl</th>\n", "      <th>num_bc_sats</th>\n", "      <th>num_bc_tl</th>\n", "      <th>num_il_tl</th>\n", "      <th>num_op_rev_tl</th>\n", "      <th>num_rev_accts</th>\n", "      <th>num_rev_tl_bal_gt_0</th>\n", "      <th>num_sats</th>\n", "      <th>num_tl_120dpd_2m</th>\n", "      <th>num_tl_30dpd</th>\n", "      <th>num_tl_90g_dpd_24m</th>\n", "      <th>num_tl_op_past_12m</th>\n", "      <th>pct_tl_nvr_dlq</th>\n", "      <th>percent_bc_gt_75</th>\n", "      <th>pub_rec_bankruptcies</th>\n", "      <th>tax_liens</th>\n", "      <th>tot_hi_cred_lim</th>\n", "      <th>total_bal_ex_mort</th>\n", "      <th>total_bc_limit</th>\n", "      <th>total_il_high_credit_limit</th>\n", "      <th>revol_bal_joint</th>\n", "      <th>sec_app_fico_range_low</th>\n", "      <th>sec_app_fico_range_high</th>\n", "      <th>sec_app_earliest_cr_line</th>\n", "      <th>sec_app_inq_last_6mths</th>\n", "      <th>sec_app_mort_acc</th>\n", "      <th>sec_app_open_acc</th>\n", "      <th>sec_app_revol_util</th>\n", "      <th>sec_app_open_act_il</th>\n", "      <th>sec_app_num_rev_accts</th>\n", "      <th>sec_app_chargeoff_within_12_mths</th>\n", "      <th>sec_app_collections_12_mths_ex_med</th>\n", "      <th>sec_app_mths_since_last_major_derog</th>\n", "      <th>hardship_flag</th>\n", "      <th>hardship_type</th>\n", "      <th>hardship_reason</th>\n", "      <th>hardship_status</th>\n", "      <th>deferral_term</th>\n", "      <th>hardship_amount</th>\n", "      <th>hardship_start_date</th>\n", "      <th>hardship_end_date</th>\n", "      <th>payment_plan_start_date</th>\n", "      <th>hardship_length</th>\n", "      <th>hardship_dpd</th>\n", "      <th>hardship_loan_status</th>\n", "      <th>orig_projected_additional_accrued_interest</th>\n", "      <th>hardship_payoff_balance_amount</th>\n", "      <th>hardship_last_payment_amount</th>\n", "      <th>disbursement_method</th>\n", "      <th>debt_settlement_flag</th>\n", "      <th>debt_settlement_flag_date</th>\n", "      <th>settlement_status</th>\n", "      <th>settlement_date</th>\n", "      <th>settlement_amount</th>\n", "      <th>settlement_percentage</th>\n", "      <th>settlement_term</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>68407277</td>\n", "      <td>NaN</td>\n", "      <td>3600.0</td>\n", "      <td>3600.0</td>\n", "      <td>3600.0</td>\n", "      <td>36 months</td>\n", "      <td>13.99</td>\n", "      <td>123.03</td>\n", "      <td>C</td>\n", "      <td>C4</td>\n", "      <td>leadman</td>\n", "      <td>10+ years</td>\n", "      <td>MORTGAGE</td>\n", "      <td>55000.0</td>\n", "      <td>Not Verified</td>\n", "      <td>Dec-2015</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://lendingclub.com/browse/loanDetail.acti...</td>\n", "      <td>NaN</td>\n", "      <td>debt_consolidation</td>\n", "      <td>Debt consolidation</td>\n", "      <td>190xx</td>\n", "      <td>PA</td>\n", "      <td>5.91</td>\n", "      <td>0.0</td>\n", "      <td>Aug-2003</td>\n", "      <td>675.0</td>\n", "      <td>679.0</td>\n", "      <td>1.0</td>\n", "      <td>30.0</td>\n", "      <td>NaN</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>2765.0</td>\n", "      <td>29.7</td>\n", "      <td>13.0</td>\n", "      <td>w</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>4421.723917</td>\n", "      <td>4421.72</td>\n", "      <td>3600.00</td>\n", "      <td>821.72</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Jan-2019</td>\n", "      <td>122.67</td>\n", "      <td>NaN</td>\n", "      <td>Mar-2019</td>\n", "      <td>564.0</td>\n", "      <td>560.0</td>\n", "      <td>0.0</td>\n", "      <td>30.0</td>\n", "      <td>1.0</td>\n", "      <td>Individual</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>722.0</td>\n", "      <td>144904.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>21.0</td>\n", "      <td>4981.0</td>\n", "      <td>36.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>722.0</td>\n", "      <td>34.0</td>\n", "      <td>9300.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>20701.0</td>\n", "      <td>1506.0</td>\n", "      <td>37.2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>148.0</td>\n", "      <td>128.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>69.0</td>\n", "      <td>4.0</td>\n", "      <td>69.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>4.0</td>\n", "      <td>9.0</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>76.9</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>178050.0</td>\n", "      <td>7746.0</td>\n", "      <td>2400.0</td>\n", "      <td>13734.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Cash</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>68355089</td>\n", "      <td>NaN</td>\n", "      <td>24700.0</td>\n", "      <td>24700.0</td>\n", "      <td>24700.0</td>\n", "      <td>36 months</td>\n", "      <td>11.99</td>\n", "      <td>820.28</td>\n", "      <td>C</td>\n", "      <td>C1</td>\n", "      <td>Engineer</td>\n", "      <td>10+ years</td>\n", "      <td>MORTGAGE</td>\n", "      <td>65000.0</td>\n", "      <td>Not Verified</td>\n", "      <td>Dec-2015</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://lendingclub.com/browse/loanDetail.acti...</td>\n", "      <td>NaN</td>\n", "      <td>small_business</td>\n", "      <td>Business</td>\n", "      <td>577xx</td>\n", "      <td>SD</td>\n", "      <td>16.06</td>\n", "      <td>1.0</td>\n", "      <td>Dec-1999</td>\n", "      <td>715.0</td>\n", "      <td>719.0</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>22.0</td>\n", "      <td>0.0</td>\n", "      <td>21470.0</td>\n", "      <td>19.2</td>\n", "      <td>38.0</td>\n", "      <td>w</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>25679.660000</td>\n", "      <td>25679.66</td>\n", "      <td>24700.00</td>\n", "      <td>979.66</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Jun-2016</td>\n", "      <td>926.35</td>\n", "      <td>NaN</td>\n", "      <td>Mar-2019</td>\n", "      <td>699.0</td>\n", "      <td>695.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>Individual</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>204396.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>19.0</td>\n", "      <td>18005.0</td>\n", "      <td>73.0</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>6472.0</td>\n", "      <td>29.0</td>\n", "      <td>111800.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>4.0</td>\n", "      <td>9733.0</td>\n", "      <td>57830.0</td>\n", "      <td>27.1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>113.0</td>\n", "      <td>192.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>13.0</td>\n", "      <td>17.0</td>\n", "      <td>6.0</td>\n", "      <td>20.0</td>\n", "      <td>27.0</td>\n", "      <td>5.0</td>\n", "      <td>22.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>97.4</td>\n", "      <td>7.7</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>314017.0</td>\n", "      <td>39475.0</td>\n", "      <td>79300.0</td>\n", "      <td>24667.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Cash</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>68341763</td>\n", "      <td>NaN</td>\n", "      <td>20000.0</td>\n", "      <td>20000.0</td>\n", "      <td>20000.0</td>\n", "      <td>60 months</td>\n", "      <td>10.78</td>\n", "      <td>432.66</td>\n", "      <td>B</td>\n", "      <td>B4</td>\n", "      <td>truck driver</td>\n", "      <td>10+ years</td>\n", "      <td>MORTGAGE</td>\n", "      <td>63000.0</td>\n", "      <td>Not Verified</td>\n", "      <td>Dec-2015</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://lendingclub.com/browse/loanDetail.acti...</td>\n", "      <td>NaN</td>\n", "      <td>home_improvement</td>\n", "      <td>NaN</td>\n", "      <td>605xx</td>\n", "      <td>IL</td>\n", "      <td>10.78</td>\n", "      <td>0.0</td>\n", "      <td>Aug-2000</td>\n", "      <td>695.0</td>\n", "      <td>699.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>7869.0</td>\n", "      <td>56.2</td>\n", "      <td>18.0</td>\n", "      <td>w</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>22705.924294</td>\n", "      <td>22705.92</td>\n", "      <td>20000.00</td>\n", "      <td>2705.92</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Jun-2017</td>\n", "      <td>15813.30</td>\n", "      <td>NaN</td>\n", "      <td>Mar-2019</td>\n", "      <td>704.0</td>\n", "      <td>700.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>Joint App</td>\n", "      <td>71000.0</td>\n", "      <td>13.85</td>\n", "      <td>Not Verified</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>189699.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>19.0</td>\n", "      <td>10827.0</td>\n", "      <td>73.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>2081.0</td>\n", "      <td>65.0</td>\n", "      <td>14000.0</td>\n", "      <td>2.0</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>31617.0</td>\n", "      <td>2737.0</td>\n", "      <td>55.9</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>125.0</td>\n", "      <td>184.0</td>\n", "      <td>14.0</td>\n", "      <td>14.0</td>\n", "      <td>5.0</td>\n", "      <td>101.0</td>\n", "      <td>NaN</td>\n", "      <td>10.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>3.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100.0</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>218418.0</td>\n", "      <td>18696.0</td>\n", "      <td>6200.0</td>\n", "      <td>14877.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Cash</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>66310712</td>\n", "      <td>NaN</td>\n", "      <td>35000.0</td>\n", "      <td>35000.0</td>\n", "      <td>35000.0</td>\n", "      <td>60 months</td>\n", "      <td>14.85</td>\n", "      <td>829.90</td>\n", "      <td>C</td>\n", "      <td>C5</td>\n", "      <td>Information Systems Officer</td>\n", "      <td>10+ years</td>\n", "      <td>MORTGAGE</td>\n", "      <td>110000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>Dec-2015</td>\n", "      <td>Current</td>\n", "      <td>n</td>\n", "      <td>https://lendingclub.com/browse/loanDetail.acti...</td>\n", "      <td>NaN</td>\n", "      <td>debt_consolidation</td>\n", "      <td>Debt consolidation</td>\n", "      <td>076xx</td>\n", "      <td>NJ</td>\n", "      <td>17.06</td>\n", "      <td>0.0</td>\n", "      <td>Sep-2008</td>\n", "      <td>785.0</td>\n", "      <td>789.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13.0</td>\n", "      <td>0.0</td>\n", "      <td>7802.0</td>\n", "      <td>11.6</td>\n", "      <td>17.0</td>\n", "      <td>w</td>\n", "      <td>15897.65</td>\n", "      <td>15897.65</td>\n", "      <td>31464.010000</td>\n", "      <td>31464.01</td>\n", "      <td>19102.35</td>\n", "      <td>12361.66</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Feb-2019</td>\n", "      <td>829.90</td>\n", "      <td>Apr-2019</td>\n", "      <td>Mar-2019</td>\n", "      <td>679.0</td>\n", "      <td>675.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>Individual</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>301500.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>23.0</td>\n", "      <td>12609.0</td>\n", "      <td>70.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>6987.0</td>\n", "      <td>45.0</td>\n", "      <td>67300.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>23192.0</td>\n", "      <td>54962.0</td>\n", "      <td>12.1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>36.0</td>\n", "      <td>87.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>5.0</td>\n", "      <td>8.0</td>\n", "      <td>10.0</td>\n", "      <td>2.0</td>\n", "      <td>10.0</td>\n", "      <td>13.0</td>\n", "      <td>5.0</td>\n", "      <td>13.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>381215.0</td>\n", "      <td>52226.0</td>\n", "      <td>62500.0</td>\n", "      <td>18000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Cash</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>68476807</td>\n", "      <td>NaN</td>\n", "      <td>10400.0</td>\n", "      <td>10400.0</td>\n", "      <td>10400.0</td>\n", "      <td>60 months</td>\n", "      <td>22.45</td>\n", "      <td>289.91</td>\n", "      <td>F</td>\n", "      <td>F1</td>\n", "      <td>Contract Specialist</td>\n", "      <td>3 years</td>\n", "      <td>MORTGAGE</td>\n", "      <td>104433.0</td>\n", "      <td>Source Verified</td>\n", "      <td>Dec-2015</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://lendingclub.com/browse/loanDetail.acti...</td>\n", "      <td>NaN</td>\n", "      <td>major_purchase</td>\n", "      <td>Major purchase</td>\n", "      <td>174xx</td>\n", "      <td>PA</td>\n", "      <td>25.37</td>\n", "      <td>1.0</td>\n", "      <td>Jun-1998</td>\n", "      <td>695.0</td>\n", "      <td>699.0</td>\n", "      <td>3.0</td>\n", "      <td>12.0</td>\n", "      <td>NaN</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>21929.0</td>\n", "      <td>64.5</td>\n", "      <td>35.0</td>\n", "      <td>w</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>11740.500000</td>\n", "      <td>11740.50</td>\n", "      <td>10400.00</td>\n", "      <td>1340.50</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>Jul-2016</td>\n", "      <td>10128.96</td>\n", "      <td>NaN</td>\n", "      <td>Mar-2018</td>\n", "      <td>704.0</td>\n", "      <td>700.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>Individual</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>331730.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>14.0</td>\n", "      <td>73839.0</td>\n", "      <td>84.0</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>9702.0</td>\n", "      <td>78.0</td>\n", "      <td>34000.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>10.0</td>\n", "      <td>27644.0</td>\n", "      <td>4567.0</td>\n", "      <td>77.5</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>128.0</td>\n", "      <td>210.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>4.0</td>\n", "      <td>12.0</td>\n", "      <td>1.0</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>9.0</td>\n", "      <td>10.0</td>\n", "      <td>7.0</td>\n", "      <td>19.0</td>\n", "      <td>6.0</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>96.6</td>\n", "      <td>60.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>439570.0</td>\n", "      <td>95768.0</td>\n", "      <td>20300.0</td>\n", "      <td>88097.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Cash</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id  member_id  loan_amnt  funded_amnt  funded_amnt_inv        term  \\\n", "0  68407277        NaN     3600.0       3600.0           3600.0   36 months   \n", "1  68355089        NaN    24700.0      24700.0          24700.0   36 months   \n", "2  68341763        NaN    20000.0      20000.0          20000.0   60 months   \n", "3  66310712        NaN    35000.0      35000.0          35000.0   60 months   \n", "4  68476807        NaN    10400.0      10400.0          10400.0   60 months   \n", "\n", "   int_rate  installment grade sub_grade                    emp_title  \\\n", "0     13.99       123.03     C        C4                      leadman   \n", "1     11.99       820.28     C        C1                     Engineer   \n", "2     10.78       432.66     B        B4                 truck driver   \n", "3     14.85       829.90     C        C5  Information Systems Officer   \n", "4     22.45       289.91     F        F1          Contract Specialist   \n", "\n", "  emp_length home_ownership  annual_inc verification_status   issue_d  \\\n", "0  10+ years       MORTGAGE     55000.0        Not Verified  Dec-2015   \n", "1  10+ years       MORTGAGE     65000.0        Not Verified  Dec-2015   \n", "2  10+ years       MORTGAGE     63000.0        Not Verified  Dec-2015   \n", "3  10+ years       MORTGAGE    110000.0     Source Verified  Dec-2015   \n", "4    3 years       MORTGAGE    104433.0     Source Verified  Dec-2015   \n", "\n", "  loan_status pymnt_plan                                                url  \\\n", "0  Fully Paid          n  https://lendingclub.com/browse/loanDetail.acti...   \n", "1  Fully Paid          n  https://lendingclub.com/browse/loanDetail.acti...   \n", "2  Fully Paid          n  https://lendingclub.com/browse/loanDetail.acti...   \n", "3     Current          n  https://lendingclub.com/browse/loanDetail.acti...   \n", "4  Fully Paid          n  https://lendingclub.com/browse/loanDetail.acti...   \n", "\n", "  desc             purpose               title zip_code addr_state    dti  \\\n", "0  NaN  debt_consolidation  Debt consolidation    190xx         PA   5.91   \n", "1  NaN      small_business            Business    577xx         SD  16.06   \n", "2  NaN    home_improvement                 NaN    605xx         IL  10.78   \n", "3  NaN  debt_consolidation  Debt consolidation    076xx         NJ  17.06   \n", "4  NaN      major_purchase      Major purchase    174xx         PA  25.37   \n", "\n", "   delinq_2yrs earliest_cr_line  fico_range_low  fico_range_high  \\\n", "0          0.0         Aug-2003           675.0            679.0   \n", "1          1.0         Dec-1999           715.0            719.0   \n", "2          0.0         Aug-2000           695.0            699.0   \n", "3          0.0         Sep-2008           785.0            789.0   \n", "4          1.0         Jun-1998           695.0            699.0   \n", "\n", "   inq_last_6mths  mths_since_last_delinq  mths_since_last_record  open_acc  \\\n", "0             1.0                    30.0                     NaN       7.0   \n", "1             4.0                     6.0                     NaN      22.0   \n", "2             0.0                     NaN                     NaN       6.0   \n", "3             0.0                     NaN                     NaN      13.0   \n", "4             3.0                    12.0                     NaN      12.0   \n", "\n", "   pub_rec  revol_bal  revol_util  total_acc initial_list_status  out_prncp  \\\n", "0      0.0     2765.0        29.7       13.0                   w       0.00   \n", "1      0.0    21470.0        19.2       38.0                   w       0.00   \n", "2      0.0     7869.0        56.2       18.0                   w       0.00   \n", "3      0.0     7802.0        11.6       17.0                   w   15897.65   \n", "4      0.0    21929.0        64.5       35.0                   w       0.00   \n", "\n", "   out_prncp_inv   total_pymnt  total_pymnt_inv  total_rec_prncp  \\\n", "0           0.00   4421.723917          4421.72          3600.00   \n", "1           0.00  25679.660000         25679.66         24700.00   \n", "2           0.00  22705.924294         22705.92         20000.00   \n", "3       15897.65  31464.010000         31464.01         19102.35   \n", "4           0.00  11740.500000         11740.50         10400.00   \n", "\n", "   total_rec_int  total_rec_late_fee  recoveries  collection_recovery_fee  \\\n", "0         821.72                 0.0         0.0                      0.0   \n", "1         979.66                 0.0         0.0                      0.0   \n", "2        2705.92                 0.0         0.0                      0.0   \n", "3       12361.66                 0.0         0.0                      0.0   \n", "4        1340.50                 0.0         0.0                      0.0   \n", "\n", "  last_pymnt_d  last_pymnt_amnt next_pymnt_d last_credit_pull_d  \\\n", "0     Jan-2019           122.67          NaN           Mar-2019   \n", "1     Jun-2016           926.35          NaN           Mar-2019   \n", "2     Jun-2017         15813.30          NaN           Mar-2019   \n", "3     Feb-2019           829.90     Apr-2019           Mar-2019   \n", "4     Jul-2016         10128.96          NaN           Mar-2018   \n", "\n", "   last_fico_range_high  last_fico_range_low  collections_12_mths_ex_med  \\\n", "0                 564.0                560.0                         0.0   \n", "1                 699.0                695.0                         0.0   \n", "2                 704.0                700.0                         0.0   \n", "3                 679.0                675.0                         0.0   \n", "4                 704.0                700.0                         0.0   \n", "\n", "   mths_since_last_major_derog  policy_code application_type  \\\n", "0                         30.0          1.0       Individual   \n", "1                          NaN          1.0       Individual   \n", "2                          NaN          1.0        Joint App   \n", "3                          NaN          1.0       Individual   \n", "4                          NaN          1.0       Individual   \n", "\n", "   annual_inc_joint  dti_joint verification_status_joint  acc_now_delinq  \\\n", "0               NaN        NaN                       NaN             0.0   \n", "1               NaN        NaN                       NaN             0.0   \n", "2           71000.0      13.85              Not Verified             0.0   \n", "3               NaN        NaN                       NaN             0.0   \n", "4               NaN        NaN                       NaN             0.0   \n", "\n", "   tot_coll_amt  tot_cur_bal  open_acc_6m  open_act_il  open_il_12m  \\\n", "0         722.0     144904.0          2.0          2.0          0.0   \n", "1           0.0     204396.0          1.0          1.0          0.0   \n", "2           0.0     189699.0          0.0          1.0          0.0   \n", "3           0.0     301500.0          1.0          1.0          0.0   \n", "4           0.0     331730.0          1.0          3.0          0.0   \n", "\n", "   open_il_24m  mths_since_rcnt_il  total_bal_il  il_util  open_rv_12m  \\\n", "0          1.0                21.0        4981.0     36.0          3.0   \n", "1          1.0                19.0       18005.0     73.0          2.0   \n", "2          4.0                19.0       10827.0     73.0          0.0   \n", "3          1.0                23.0       12609.0     70.0          1.0   \n", "4          3.0                14.0       73839.0     84.0          4.0   \n", "\n", "   open_rv_24m  max_bal_bc  all_util  total_rev_hi_lim  inq_fi  total_cu_tl  \\\n", "0          3.0       722.0      34.0            9300.0     3.0          1.0   \n", "1          3.0      6472.0      29.0          111800.0     0.0          0.0   \n", "2          2.0      2081.0      65.0           14000.0     2.0          5.0   \n", "3          1.0      6987.0      45.0           67300.0     0.0          1.0   \n", "4          7.0      9702.0      78.0           34000.0     2.0          1.0   \n", "\n", "   inq_last_12m  acc_open_past_24mths  avg_cur_bal  bc_open_to_buy  bc_util  \\\n", "0           4.0                   4.0      20701.0          1506.0     37.2   \n", "1           6.0                   4.0       9733.0         57830.0     27.1   \n", "2           1.0                   6.0      31617.0          2737.0     55.9   \n", "3           0.0                   2.0      23192.0         54962.0     12.1   \n", "4           3.0                  10.0      27644.0          4567.0     77.5   \n", "\n", "   chargeoff_within_12_mths  delinq_amnt  mo_sin_old_il_acct  \\\n", "0                       0.0          0.0               148.0   \n", "1                       0.0          0.0               113.0   \n", "2                       0.0          0.0               125.0   \n", "3                       0.0          0.0                36.0   \n", "4                       0.0          0.0               128.0   \n", "\n", "   mo_sin_old_rev_tl_op  mo_sin_rcnt_rev_tl_op  mo_sin_rcnt_tl  mort_acc  \\\n", "0                 128.0                    3.0             3.0       1.0   \n", "1                 192.0                    2.0             2.0       4.0   \n", "2                 184.0                   14.0            14.0       5.0   \n", "3                  87.0                    2.0             2.0       1.0   \n", "4                 210.0                    4.0             4.0       6.0   \n", "\n", "   mths_since_recent_bc  mths_since_recent_bc_dlq  mths_since_recent_inq  \\\n", "0                   4.0                      69.0                    4.0   \n", "1                   2.0                       NaN                    0.0   \n", "2                 101.0                       NaN                   10.0   \n", "3                   2.0                       NaN                    NaN   \n", "4                   4.0                      12.0                    1.0   \n", "\n", "   mths_since_recent_revol_delinq  num_accts_ever_120_pd  num_actv_bc_tl  \\\n", "0                            69.0                    2.0             2.0   \n", "1                             6.0                    0.0             5.0   \n", "2                             NaN                    0.0             2.0   \n", "3                             NaN                    0.0             4.0   \n", "4                            12.0                    0.0             4.0   \n", "\n", "   num_actv_rev_tl  num_bc_sats  num_bc_tl  num_il_tl  num_op_rev_tl  \\\n", "0              4.0          2.0        5.0        3.0            4.0   \n", "1              5.0         13.0       17.0        6.0           20.0   \n", "2              3.0          2.0        4.0        6.0            4.0   \n", "3              5.0          8.0       10.0        2.0           10.0   \n", "4              6.0          5.0        9.0       10.0            7.0   \n", "\n", "   num_rev_accts  num_rev_tl_bal_gt_0  num_sats  num_tl_120dpd_2m  \\\n", "0            9.0                  4.0       7.0               0.0   \n", "1           27.0                  5.0      22.0               0.0   \n", "2            7.0                  3.0       6.0               0.0   \n", "3           13.0                  5.0      13.0               0.0   \n", "4           19.0                  6.0      12.0               0.0   \n", "\n", "   num_tl_30dpd  num_tl_90g_dpd_24m  num_tl_op_past_12m  pct_tl_nvr_dlq  \\\n", "0           0.0                 0.0                 3.0            76.9   \n", "1           0.0                 0.0                 2.0            97.4   \n", "2           0.0                 0.0                 0.0           100.0   \n", "3           0.0                 0.0                 1.0           100.0   \n", "4           0.0                 0.0                 4.0            96.6   \n", "\n", "   percent_bc_gt_75  pub_rec_bankruptcies  tax_liens  tot_hi_cred_lim  \\\n", "0               0.0                   0.0        0.0         178050.0   \n", "1               7.7                   0.0        0.0         314017.0   \n", "2              50.0                   0.0        0.0         218418.0   \n", "3               0.0                   0.0        0.0         381215.0   \n", "4              60.0                   0.0        0.0         439570.0   \n", "\n", "   total_bal_ex_mort  total_bc_limit  total_il_high_credit_limit  \\\n", "0             7746.0          2400.0                     13734.0   \n", "1            39475.0         79300.0                     24667.0   \n", "2            18696.0          6200.0                     14877.0   \n", "3            52226.0         62500.0                     18000.0   \n", "4            95768.0         20300.0                     88097.0   \n", "\n", "   revol_bal_joint  sec_app_fico_range_low  sec_app_fico_range_high  \\\n", "0              NaN                     NaN                      NaN   \n", "1              NaN                     NaN                      NaN   \n", "2              NaN                     NaN                      NaN   \n", "3              NaN                     NaN                      NaN   \n", "4              NaN                     NaN                      NaN   \n", "\n", "  sec_app_earliest_cr_line  sec_app_inq_last_6mths  sec_app_mort_acc  \\\n", "0                      NaN                     NaN               NaN   \n", "1                      NaN                     NaN               NaN   \n", "2                      NaN                     NaN               NaN   \n", "3                      NaN                     NaN               NaN   \n", "4                      NaN                     NaN               NaN   \n", "\n", "   sec_app_open_acc  sec_app_revol_util  sec_app_open_act_il  \\\n", "0               NaN                 NaN                  NaN   \n", "1               NaN                 NaN                  NaN   \n", "2               NaN                 NaN                  NaN   \n", "3               NaN                 NaN                  NaN   \n", "4               NaN                 NaN                  NaN   \n", "\n", "   sec_app_num_rev_accts  sec_app_chargeoff_within_12_mths  \\\n", "0                    NaN                               NaN   \n", "1                    NaN                               NaN   \n", "2                    NaN                               NaN   \n", "3                    NaN                               NaN   \n", "4                    NaN                               NaN   \n", "\n", "   sec_app_collections_12_mths_ex_med  sec_app_mths_since_last_major_derog  \\\n", "0                                 NaN                                  NaN   \n", "1                                 NaN                                  NaN   \n", "2                                 NaN                                  NaN   \n", "3                                 NaN                                  NaN   \n", "4                                 NaN                                  NaN   \n", "\n", "  hardship_flag hardship_type hardship_reason hardship_status  deferral_term  \\\n", "0             N           NaN             NaN             NaN            NaN   \n", "1             N           NaN             NaN             NaN            NaN   \n", "2             N           NaN             NaN             NaN            NaN   \n", "3             N           NaN             NaN             NaN            NaN   \n", "4             N           NaN             NaN             NaN            NaN   \n", "\n", "   hardship_amount hardship_start_date hardship_end_date  \\\n", "0              NaN                 NaN               NaN   \n", "1              NaN                 NaN               NaN   \n", "2              NaN                 NaN               NaN   \n", "3              NaN                 NaN               NaN   \n", "4              NaN                 NaN               NaN   \n", "\n", "  payment_plan_start_date  hardship_length  hardship_dpd hardship_loan_status  \\\n", "0                     NaN              NaN           NaN                  NaN   \n", "1                     NaN              NaN           NaN                  NaN   \n", "2                     NaN              NaN           NaN                  NaN   \n", "3                     NaN              NaN           NaN                  NaN   \n", "4                     NaN              NaN           NaN                  NaN   \n", "\n", "   orig_projected_additional_accrued_interest  hardship_payoff_balance_amount  \\\n", "0                                         NaN                             NaN   \n", "1                                         NaN                             NaN   \n", "2                                         NaN                             NaN   \n", "3                                         NaN                             NaN   \n", "4                                         NaN                             NaN   \n", "\n", "   hardship_last_payment_amount disbursement_method debt_settlement_flag  \\\n", "0                           NaN                Cash                    N   \n", "1                           NaN                Cash                    N   \n", "2                           NaN                Cash                    N   \n", "3                           NaN                Cash                    N   \n", "4                           NaN                Cash                    N   \n", "\n", "  debt_settlement_flag_date settlement_status settlement_date  \\\n", "0                       NaN               NaN             NaN   \n", "1                       NaN               NaN             NaN   \n", "2                       NaN               NaN             NaN   \n", "3                       NaN               NaN             NaN   \n", "4                       NaN               NaN             NaN   \n", "\n", "   settlement_amount  settlement_percentage  settlement_term  \n", "0                NaN                    NaN              NaN  \n", "1                NaN                    NaN              NaN  \n", "2                NaN                    NaN              NaN  \n", "3                NaN                    NaN              NaN  \n", "4                NaN                    NaN              NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "d2b887f7", "metadata": {}, "outputs": [{"ename": "MemoryError", "evalue": "Unable to allocate 172. MiB for an array with shape (10, 2260701) and data type float64", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                               <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[18], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdescribe\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\generic.py:11995\u001b[0m, in \u001b[0;36mNDFrame.describe\u001b[1;34m(self, percentiles, include, exclude)\u001b[0m\n\u001b[0;32m  11753\u001b[0m \u001b[38;5;129m@final\u001b[39m\n\u001b[0;32m  11754\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mdescribe\u001b[39m(\n\u001b[0;32m  11755\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m  11758\u001b[0m     exclude\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m  11759\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Self:\n\u001b[0;32m  11760\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m  11761\u001b[0m \u001b[38;5;124;03m    Generate descriptive statistics.\u001b[39;00m\n\u001b[0;32m  11762\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m  11993\u001b[0m \u001b[38;5;124;03m    max            NaN      3.0\u001b[39;00m\n\u001b[0;32m  11994\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m> 11995\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdescribe_ndframe\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m  11996\u001b[0m \u001b[43m        \u001b[49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m  11997\u001b[0m \u001b[43m        \u001b[49m\u001b[43minclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minclude\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m  11998\u001b[0m \u001b[43m        \u001b[49m\u001b[43mexclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexclude\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m  11999\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpercentiles\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpercentiles\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m  12000\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdescribe\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py:97\u001b[0m, in \u001b[0;36mdescribe_ndframe\u001b[1;34m(obj, include, exclude, percentiles)\u001b[0m\n\u001b[0;32m     90\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m     91\u001b[0m     describer \u001b[38;5;241m=\u001b[39m DataFrameDescriber(\n\u001b[0;32m     92\u001b[0m         obj\u001b[38;5;241m=\u001b[39mcast(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataFrame\u001b[39m\u001b[38;5;124m\"\u001b[39m, obj),\n\u001b[0;32m     93\u001b[0m         include\u001b[38;5;241m=\u001b[39minclude,\n\u001b[0;32m     94\u001b[0m         exclude\u001b[38;5;241m=\u001b[39mexclude,\n\u001b[0;32m     95\u001b[0m     )\n\u001b[1;32m---> 97\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mdescriber\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdescribe\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpercentiles\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpercentiles\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     98\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m cast(NDFrameT, result)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py:167\u001b[0m, in \u001b[0;36mDataFrameDescriber.describe\u001b[1;34m(self, percentiles)\u001b[0m\n\u001b[0;32m    166\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mdescribe\u001b[39m(\u001b[38;5;28mself\u001b[39m, percentiles: Sequence[\u001b[38;5;28mfloat\u001b[39m] \u001b[38;5;241m|\u001b[39m np\u001b[38;5;241m.\u001b[39mndarray) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m DataFrame:\n\u001b[1;32m--> 167\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    169\u001b[0m     ldesc: \u001b[38;5;28mlist\u001b[39m[Series] \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m    170\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m _, series \u001b[38;5;129;01min\u001b[39;00m data\u001b[38;5;241m.\u001b[39mitems():\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py:188\u001b[0m, in \u001b[0;36mDataFrameDescriber._select_data\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    185\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minclude \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;129;01mand\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexclude \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    186\u001b[0m     \u001b[38;5;66;03m# when some numerics are found, keep only numerics\u001b[39;00m\n\u001b[0;32m    187\u001b[0m     default_include: \u001b[38;5;28mlist\u001b[39m[npt\u001b[38;5;241m.\u001b[39mDTypeLike] \u001b[38;5;241m=\u001b[39m [np\u001b[38;5;241m.\u001b[39mnumber, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdatetime\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m--> 188\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect_dtypes\u001b[49m\u001b[43m(\u001b[49m\u001b[43minclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdefault_include\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    189\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(data\u001b[38;5;241m.\u001b[39mcolumns) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    190\u001b[0m         data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\frame.py:5098\u001b[0m, in \u001b[0;36mDataFrame.select_dtypes\u001b[1;34m(self, include, exclude)\u001b[0m\n\u001b[0;32m   5094\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m   5096\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m-> 5098\u001b[0m mgr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_data_subset\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpredicate\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdeep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[0;32m   5099\u001b[0m \u001b[38;5;66;03m# error: Incompatible return value type (got \"DataFrame\", expected \"Self\")\u001b[39;00m\n\u001b[0;32m   5100\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_constructor_from_mgr(mgr, axes\u001b[38;5;241m=\u001b[39mmgr\u001b[38;5;241m.\u001b[39maxes)\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\internals\\managers.py:593\u001b[0m, in \u001b[0;36mBaseBlockManager.copy\u001b[1;34m(self, deep)\u001b[0m\n\u001b[0;32m    590\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    591\u001b[0m         new_axes \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes)\n\u001b[1;32m--> 593\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcopy\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdeep\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    594\u001b[0m res\u001b[38;5;241m.\u001b[39maxes \u001b[38;5;241m=\u001b[39m new_axes\n\u001b[0;32m    596\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    597\u001b[0m     \u001b[38;5;66;03m# Avoid needing to re-compute these\u001b[39;00m\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\internals\\managers.py:363\u001b[0m, in \u001b[0;36mBaseBlockManager.apply\u001b[1;34m(self, f, align_keys, **kwargs)\u001b[0m\n\u001b[0;32m    361\u001b[0m         applied \u001b[38;5;241m=\u001b[39m b\u001b[38;5;241m.\u001b[39mapply(f, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    362\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 363\u001b[0m         applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(b, f)(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    364\u001b[0m     result_blocks \u001b[38;5;241m=\u001b[39m extend_blocks(applied, result_blocks)\n\u001b[0;32m    366\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39mfrom_blocks(result_blocks, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes)\n", "File \u001b[1;32md:\\Dhia\\ING INFO 2\\Stage d'ete\\Proxym\\Loan Request Approval Prediction\\.venv\\lib\\site-packages\\pandas\\core\\internals\\blocks.py:822\u001b[0m, in \u001b[0;36mBlock.copy\u001b[1;34m(self, deep)\u001b[0m\n\u001b[0;32m    820\u001b[0m refs: BlockValuesRefs \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    821\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m deep:\n\u001b[1;32m--> 822\u001b[0m     values \u001b[38;5;241m=\u001b[39m \u001b[43mvalues\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    823\u001b[0m     refs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    824\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[1;31mMemoryError\u001b[0m: Unable to allocate 172. MiB for an array with shape (10, 2260701) and data type float64"]}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 19, "id": "1154b9f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2260701 entries, 0 to 2260700\n", "Columns: 151 entries, id to settlement_term\n", "dtypes: float64(113), object(38)\n", "memory usage: 2.5+ GB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "87461547", "metadata": {}, "outputs": [], "source": ["df_number_missing = df.isnull().sum()\n", "df_percentage_missing = df_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "2de2e685", "metadata": {}, "outputs": [], "source": ["df[\"emp_length\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "3ac5c5b8", "metadata": {}, "outputs": [], "source": ["dict_df = {}\n", "for col in df:\n", "    dict_df[col] = col\n", "dict_df"]}, {"cell_type": "code", "execution_count": null, "id": "13606ca5", "metadata": {}, "outputs": [], "source": ["dict_number_missing = dict_df.copy()\n", "dict_percentage_missing = dict_df.copy()\n", "dict_number_missing.update(df_number_missing)\n", "dict_percentage_missing.update(df_percentage_missing)\n", "dict_percentage_missing, dict_number_missing"]}, {"cell_type": "code", "execution_count": null, "id": "df6a5ba8", "metadata": {}, "outputs": [], "source": ["pd.DataFrame([dict_number_missing, dict_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "code", "execution_count": null, "id": "826c868c", "metadata": {}, "outputs": [], "source": ["for key in dict_percentage_missing.keys():\n", "    if dict_percentage_missing[key] > 0:\n", "        print(key, dict_percentage_missing[key])"]}, {"cell_type": "code", "execution_count": null, "id": "b7e73348", "metadata": {}, "outputs": [], "source": ["for col in df:\n", "    print(\"=\"*30)\n", "    print(col)\n", "    print(df[col].unique())\n", "    print(df[col].value_counts())\n", "print(\"=\"*30)\n"]}, {"cell_type": "code", "execution_count": null, "id": "89d69d9d", "metadata": {}, "outputs": [], "source": ["# Convert date columns to datetime format\n", "df['Application Date'] = pd.to_datetime(df['Application Date'])"]}, {"cell_type": "code", "execution_count": null, "id": "c6ee084e", "metadata": {}, "outputs": [], "source": ["df[\"Application Date\"][1]"]}, {"cell_type": "code", "execution_count": null, "id": "cee2152e", "metadata": {}, "outputs": [], "source": ["earliest_date = df['Application Date'].min()\n", "latest_date = df['Application Date'].max()\n", "\n", "print(\"Earliest:\", earliest_date)\n", "print(\"Latest:\", latest_date)"]}, {"cell_type": "markdown", "id": "28ea1e9d", "metadata": {}, "source": ["## Filtering loans according to updated risk score (post 2013-11-05)"]}, {"cell_type": "code", "execution_count": null, "id": "50263312", "metadata": {}, "outputs": [], "source": ["cutoff_date = pd.to_datetime('2013-11-05')\n", "print(cutoff_date)\n", "df_post_nov5 = df[df['Application Date'] > cutoff_date]\n", "df_post_nov5.shape, df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7490075c", "metadata": {}, "outputs": [], "source": ["df_post_nov5.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3cf3335b", "metadata": {}, "outputs": [], "source": ["df[df[\"Risk_Score\"] > 850].shape"]}, {"cell_type": "code", "execution_count": null, "id": "d733bda6", "metadata": {}, "outputs": [], "source": ["sorted(df[\"Risk_Score\"].unique(), reverse=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9e6ef416", "metadata": {}, "outputs": [], "source": ["sorted(df_post_nov5[\"Risk_Score\"].unique(), reverse=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d483d3bb", "metadata": {}, "outputs": [], "source": ["df_post_nov5_number_missing = df_post_nov5.isnull().sum()\n", "df_post_nov5_percentage_missing = df_post_nov5_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "39bcec96", "metadata": {}, "outputs": [], "source": ["dict_post_nov5_number_missing = dict_df.copy()\n", "dict_post_nov5_percentage_missing = dict_df.copy()\n", "dict_post_nov5_number_missing.update(dict_number_missing)\n", "dict_post_nov5_percentage_missing.update(dict_percentage_missing)\n", "dict_post_nov5_percentage_missing, dict_post_nov5_number_missing"]}, {"cell_type": "code", "execution_count": null, "id": "51485f49", "metadata": {}, "outputs": [], "source": ["pd.DataFrame([dict_post_nov5_number_missing, dict_post_nov5_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "markdown", "id": "b5cfc09b", "metadata": {}, "source": ["## Checking Risk score missing data distribution"]}, {"cell_type": "code", "execution_count": null, "id": "7bca7380", "metadata": {}, "outputs": [], "source": ["# Empty risk score values post 2013-11-05\n", "len_na_post_nov5 = df_post_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_post_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "id": "c9341ba7", "metadata": {}, "outputs": [], "source": ["# Empty risk score values pre 2013-11-05\n", "df_pre_nov5 = df[df['Application Date'] < cutoff_date]\n", "len_na_post_nov5 = df_pre_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_pre_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "id": "98e7288f", "metadata": {}, "outputs": [], "source": ["# Empty risk score values on 2013-11-05\n", "df_pre_nov5 = df[df['Application Date'] == cutoff_date]\n", "len_na_post_nov5 = df_pre_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_pre_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "markdown", "id": "0bbca6b5", "metadata": {}, "source": ["## Cleaning database"]}, {"cell_type": "code", "execution_count": null, "id": "6d2fb465", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean = df_post_nov5.dropna()"]}, {"cell_type": "code", "execution_count": null, "id": "4a1e6033", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean.shape"]}, {"cell_type": "code", "execution_count": null, "id": "34ffab97", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "7c07e11f", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean_number_missing = df_post_nov5_clean.isnull().sum()\n", "df_post_nov5_clean_percentage_missing = df_post_nov5_clean_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": null, "id": "f51d3512", "metadata": {}, "outputs": [], "source": ["dict_post_nov5_clean_number_missing = dict_df.copy()\n", "dict_post_nov5_clean_percentage_missing = dict_df.copy()\n", "dict_post_nov5_clean_number_missing.update(df_post_nov5_clean_number_missing)\n", "dict_post_nov5_clean_percentage_missing.update(df_post_nov5_clean_percentage_missing)\n", "dict_post_nov5_clean_percentage_missing, dict_post_nov5_clean_number_missing"]}, {"cell_type": "code", "execution_count": null, "id": "e0319422", "metadata": {}, "outputs": [], "source": ["pd.DataFrame([dict_post_nov5_clean_number_missing, dict_post_nov5_clean_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "code", "execution_count": null, "id": "e4b6a1fd", "metadata": {}, "outputs": [], "source": ["SAVE_PATH =pl.Path.cwd() / \"data\" / \"Full Lending Club loan data\" / \"rejected_2007_to_2018q4\" / \"rejected_clean.csv\"\n", "df_post_nov5_clean.to_csv(SAVE_PATH, index=False)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}