{"cells": [{"cell_type": "code", "execution_count": 3, "id": "3e6092f5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import pathlib as pl\n", "import warnings\n", "# warnings.filterwarnings('ignore')\n", "# Set display options for better readability\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)"]}, {"cell_type": "code", "execution_count": 4, "id": "2dc25f48", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 27.2 s\n", "Wall time: 29.7 s\n"]}, {"data": {"text/plain": ["(27648741, 9)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "\n", "DATA_PATH = pl.Path.cwd() / \"data\" / \"Full Lending Club loan data\" / \"rejected_2007_to_2018q4\" / \"rejected_2007_to_2018Q4.csv\"\n", "df = pd.read_csv(DATA_PATH)\n", "# df = pd.read_csv(DATA_PATH, nrows=10)\n", "df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "ef08a677", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount Requested</th>\n", "      <th>Application Date</th>\n", "      <th>Loan Title</th>\n", "      <th>Risk_Score</th>\n", "      <th>Debt-To-Income Ratio</th>\n", "      <th>Zip Code</th>\n", "      <th>State</th>\n", "      <th>Employment Length</th>\n", "      <th>Policy Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1000.0</td>\n", "      <td>2007-05-26</td>\n", "      <td>Wedding Covered but No Honeymoon</td>\n", "      <td>693.0</td>\n", "      <td>10%</td>\n", "      <td>481xx</td>\n", "      <td>NM</td>\n", "      <td>4 years</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1000.0</td>\n", "      <td>2007-05-26</td>\n", "      <td>Consolidating Debt</td>\n", "      <td>703.0</td>\n", "      <td>10%</td>\n", "      <td>010xx</td>\n", "      <td>MA</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11000.0</td>\n", "      <td>2007-05-27</td>\n", "      <td>Want to consolidate my debt</td>\n", "      <td>715.0</td>\n", "      <td>10%</td>\n", "      <td>212xx</td>\n", "      <td>MD</td>\n", "      <td>1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6000.0</td>\n", "      <td>2007-05-27</td>\n", "      <td>waksman</td>\n", "      <td>698.0</td>\n", "      <td>38.64%</td>\n", "      <td>017xx</td>\n", "      <td>MA</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1500.0</td>\n", "      <td>2007-05-27</td>\n", "      <td>mdrigo</td>\n", "      <td>509.0</td>\n", "      <td>9.43%</td>\n", "      <td>209xx</td>\n", "      <td>MD</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Amount Requested Application Date                        Loan Title  \\\n", "0            1000.0       2007-05-26  Wedding Covered but No Honeymoon   \n", "1            1000.0       2007-05-26                Consolidating Debt   \n", "2           11000.0       2007-05-27       Want to consolidate my debt   \n", "3            6000.0       2007-05-27                           waksman   \n", "4            1500.0       2007-05-27                            mdrigo   \n", "\n", "   Risk_Score Debt-To-Income Ratio Zip Code State Employment Length  \\\n", "0       693.0                  10%    481xx    NM           4 years   \n", "1       703.0                  10%    010xx    MA          < 1 year   \n", "2       715.0                  10%    212xx    MD            1 year   \n", "3       698.0               38.64%    017xx    MA          < 1 year   \n", "4       509.0                9.43%    209xx    MD          < 1 year   \n", "\n", "   Policy Code  \n", "0          0.0  \n", "1          0.0  \n", "2          0.0  \n", "3          0.0  \n", "4          0.0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "d2b887f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount Requested</th>\n", "      <th>Risk_Score</th>\n", "      <th>Policy Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2.764874e+07</td>\n", "      <td>9.151111e+06</td>\n", "      <td>2.764782e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.313324e+04</td>\n", "      <td>6.281721e+02</td>\n", "      <td>6.375113e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.500964e+04</td>\n", "      <td>8.993679e+01</td>\n", "      <td>1.127368e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.800000e+03</td>\n", "      <td>5.910000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000e+04</td>\n", "      <td>6.370000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.000000e+04</td>\n", "      <td>6.750000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.400000e+06</td>\n", "      <td>9.900000e+02</td>\n", "      <td>2.000000e+00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Amount Requested    Risk_Score   Policy Code\n", "count      2.764874e+07  9.151111e+06  2.764782e+07\n", "mean       1.313324e+04  6.281721e+02  6.375113e-03\n", "std        1.500964e+04  8.993679e+01  1.127368e-01\n", "min        0.000000e+00  0.000000e+00  0.000000e+00\n", "25%        4.800000e+03  5.910000e+02  0.000000e+00\n", "50%        1.000000e+04  6.370000e+02  0.000000e+00\n", "75%        2.000000e+04  6.750000e+02  0.000000e+00\n", "max        1.400000e+06  9.900000e+02  2.000000e+00"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 7, "id": "1154b9f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 27648741 entries, 0 to 27648740\n", "Data columns (total 9 columns):\n", " #   Column                Dtype  \n", "---  ------                -----  \n", " 0   Amount Requested      float64\n", " 1   Application Date      object \n", " 2   Loan Title            object \n", " 3   Risk_Score            float64\n", " 4   Debt-To-Income Ratio  object \n", " 5   Zip Code              object \n", " 6   State                 object \n", " 7   Employment Length     object \n", " 8   Policy Code           float64\n", "dtypes: float64(3), object(6)\n", "memory usage: 1.9+ GB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 8, "id": "87461547", "metadata": {}, "outputs": [], "source": ["df_number_missing = df.isnull().sum()\n", "df_percentage_missing = df_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": 9, "id": "2de2e685", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['4 years', '< 1 year', '1 year', '3 years', '2 years', '10+ years',\n", "       '9 years', '5 years', '7 years', '6 years', '8 years', nan],\n", "      dtype=object)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Employment Length\"].unique()"]}, {"cell_type": "code", "execution_count": 10, "id": "3ac5c5b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Amount Requested': 'Amount Requested',\n", " 'Application Date': 'Application Date',\n", " 'Loan Title': 'Loan Title',\n", " 'Risk_Score': 'Risk_Score',\n", " 'Debt-To-Income Ratio': 'Debt-To-Income Ratio',\n", " 'Zip Code': 'Zip Code',\n", " 'State': 'State',\n", " 'Employment Length': 'Employment Length',\n", " 'Policy Code': 'Policy Code'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["dict_df = {}\n", "for col in df:\n", "    dict_df[col] = col\n", "dict_df"]}, {"cell_type": "code", "execution_count": 12, "id": "13606ca5", "metadata": {}, "outputs": [{"data": {"text/plain": ["({'Amount Requested': 0.0,\n", "  'Application Date': 0.0,\n", "  'Loan Title': 0.004719925583591672,\n", "  'Risk_Score': 66.90225063050791,\n", "  'Debt-To-Income Ratio': 0.0,\n", "  'Zip Code': 0.0010597227555497013,\n", "  'State': 7.95696266965646e-05,\n", "  'Employment Length': 3.440861918450464,\n", "  'Policy Code': 0.0033202235139748316},\n", " {'Amount Requested': 0,\n", "  'Application Date': 0,\n", "  'Loan Title': 1305,\n", "  'Risk_Score': 18497630,\n", "  'Debt-To-Income Ratio': 0,\n", "  'Zip Code': 293,\n", "  'State': 22,\n", "  'Employment Length': 951355,\n", "  'Policy Code': 918})"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dict_number_missing = dict_df.copy()\n", "dict_percentage_missing = dict_df.copy()\n", "dict_number_missing.update(df_number_missing)\n", "dict_percentage_missing.update(df_percentage_missing)\n", "dict_percentage_missing, dict_number_missing"]}, {"cell_type": "code", "execution_count": 13, "id": "df6a5ba8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Number of missing values</th>\n", "      <th>Percentage of missing values</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Amount Requested</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Application Date</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Loan Title</th>\n", "      <td>1305.0</td>\n", "      <td>0.004720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Risk_Score</th>\n", "      <td>18497630.0</td>\n", "      <td>66.902251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Debt-To-Income Ratio</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Zip Code</th>\n", "      <td>293.0</td>\n", "      <td>0.001060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>State</th>\n", "      <td>22.0</td>\n", "      <td>0.000080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Employment Length</th>\n", "      <td>951355.0</td>\n", "      <td>3.440862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Policy Code</th>\n", "      <td>918.0</td>\n", "      <td>0.003320</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Number of missing values  Percentage of missing values\n", "Amount Requested                           0.0                      0.000000\n", "Application Date                           0.0                      0.000000\n", "Loan Title                              1305.0                      0.004720\n", "Risk_Score                          18497630.0                     66.902251\n", "Debt-To-Income Ratio                       0.0                      0.000000\n", "Zip Code                                 293.0                      0.001060\n", "State                                     22.0                      0.000080\n", "Employment Length                     951355.0                      3.440862\n", "Policy Code                              918.0                      0.003320"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([dict_number_missing, dict_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "code", "execution_count": 14, "id": "826c868c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loan Title 0.004719925583591672\n", "Risk_Score 66.90225063050791\n", "Zip Code 0.0010597227555497013\n", "State 7.95696266965646e-05\n", "Employment Length 3.440861918450464\n", "Policy Code 0.0033202235139748316\n"]}], "source": ["for key in dict_percentage_missing.keys():\n", "    if dict_percentage_missing[key] > 0:\n", "        print(key, dict_percentage_missing[key])"]}, {"cell_type": "code", "execution_count": 15, "id": "b7e73348", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==============================\n", "Amount Requested\n", "[  1000.  11000.   6000. ...  73825. 114800.  64075.]\n", "Amount Requested\n", "10000.00      3920004\n", "5000.00       3058825\n", "20000.00      2018436\n", "1000.00       1818704\n", "15000.00      1781049\n", "35000.00      1319901\n", "2000.00       1159221\n", "25000.00      1111283\n", "3000.00       1100817\n", "30000.00       904540\n", "40000.00       873267\n", "6000.00        760245\n", "2500.00        715378\n", "4000.00        625848\n", "8000.00        598571\n", "1500.00        567892\n", "7000.00        497749\n", "12000.00       478914\n", "3500.00        313931\n", "7500.00        212418\n", "9000.00        184791\n", "18000.00       180152\n", "13000.00       156744\n", "16000.00       154120\n", "14000.00       128536\n", "11000.00       125801\n", "6500.00        122884\n", "4500.00        120212\n", "17000.00       115288\n", "5500.00        110279\n", "1200.00         93720\n", "22000.00        86753\n", "8500.00         78253\n", "28000.00        59530\n", "23000.00        51870\n", "24000.00        48476\n", "12500.00        47538\n", "19000.00        47178\n", "21000.00        46441\n", "26000.00        45271\n", "50000.00        44892\n", "27000.00        43480\n", "32000.00        41788\n", "9500.00         37975\n", "10500.00        35986\n", "100000.00       35319\n", "2600.00         29571\n", "1800.00         25223\n", "33000.00        24405\n", "15500.00        24235\n", "13500.00        22680\n", "17500.00        20565\n", "1300.00         20491\n", "1100.00         20018\n", "1600.00         19920\n", "34000.00        18711\n", "11500.00        18583\n", "29000.00        18534\n", "16500.00        17260\n", "300000.00       17190\n", "2200.00         15569\n", "14500.00        14948\n", "1400.00         14834\n", "31000.00        14812\n", "18500.00        14764\n", "20500.00        14752\n", "1700.00         14519\n", "3200.00         14250\n", "2800.00         13148\n", "38000.00        13042\n", "150000.00       13036\n", "36000.00        12456\n", "2300.00         10864\n", "75000.00        10831\n", "25500.00         9920\n", "3600.00          9658\n", "200000.00        9609\n", "3800.00          9596\n", "22500.00         9528\n", "2700.00          9438\n", "2400.00          9346\n", "4200.00          9187\n", "19500.00         8971\n", "2100.00          8095\n", "37000.00         7727\n", "60000.00         7327\n", "5200.00          7239\n", "250000.00        7160\n", "4800.00          7003\n", "3300.00          6388\n", "3700.00          6313\n", "27500.00         6263\n", "30500.00         6174\n", "21500.00         5959\n", "1900.00          5950\n", "7200.00          5861\n", "6200.00          5823\n", "5600.00          5817\n", "1250.00          5574\n", "3400.00          5546\n", "5800.00          5451\n", "6800.00          5397\n", "3100.00          5364\n", "23500.00         5301\n", "39000.00         5186\n", "24500.00         5095\n", "5300.00          4962\n", "3025.00          4859\n", "4300.00          4835\n", "7800.00          4790\n", "26500.00         4731\n", "2900.00          4700\n", "34500.00         4649\n", "28500.00         4456\n", "6300.00          4273\n", "80000.00         4219\n", "5700.00          4187\n", "4600.00          4185\n", "4700.00          4129\n", "9800.00          3973\n", "6700.00          3967\n", "32500.00         3932\n", "6025.00          3817\n", "8200.00          3790\n", "6600.00          3716\n", "5400.00          3673\n", "8800.00          3611\n", "3900.00          3376\n", "7600.00          3264\n", "6400.00          3239\n", "4400.00          3223\n", "5100.00          3203\n", "33500.00         3161\n", "29500.00         3080\n", "6100.00          3018\n", "8600.00          3005\n", "45000.00         3003\n", "8700.00          2983\n", "8400.00          2938\n", "7300.00          2929\n", "9975.00          2868\n", "4100.00          2828\n", "1750.00          2828\n", "7700.00          2796\n", "7400.00          2749\n", "9600.00          2730\n", "70000.00         2725\n", "4900.00          2696\n", "9900.00          2695\n", "8300.00          2591\n", "9200.00          2589\n", "8900.00          2490\n", "31500.00         2407\n", "6900.00          2388\n", "5900.00          2362\n", "7100.00          2194\n", "7900.00          2143\n", "9700.00          2142\n", "10600.00         2122\n", "10800.00         2049\n", "9300.00          2019\n", "10200.00         2004\n", "19975.00         1966\n", "2750.00          1960\n", "11200.00         1954\n", "125000.00        1926\n", "12600.00         1900\n", "65000.00         1895\n", "12800.00         1854\n", "8100.00          1805\n", "90000.00         1773\n", "10300.00         1746\n", "10900.00         1708\n", "2250.00          1692\n", "10100.00         1680\n", "3750.00          1675\n", "10700.00         1669\n", "5250.00          1648\n", "9400.00          1643\n", "10400.00         1612\n", "13200.00         1607\n", "11100.00         1577\n", "12200.00         1567\n", "35500.00         1548\n", "15800.00         1532\n", "12300.00         1520\n", "11300.00         1509\n", "5550.00          1495\n", "14975.00         1494\n", "11600.00         1480\n", "13800.00         1479\n", "3250.00          1474\n", "16800.00         1465\n", "11800.00         1455\n", "4975.00          1451\n", "12700.00         1449\n", "13600.00         1439\n", "9100.00          1423\n", "11700.00         1412\n", "13700.00         1402\n", "4250.00          1399\n", "11400.00         1397\n", "12900.00         1378\n", "33325.00         1358\n", "14400.00         1356\n", "14200.00         1355\n", "15600.00         1344\n", "14900.00         1325\n", "37500.00         1306\n", "13300.00         1292\n", "0.00             1288\n", "15200.00         1288\n", "12400.00         1279\n", "3975.00          1278\n", "120000.00        1263\n", "14800.00         1256\n", "13900.00         1248\n", "15300.00         1239\n", "14700.00         1233\n", "14600.00         1232\n", "4750.00          1230\n", "1050.00          1226\n", "16200.00         1225\n", "14300.00         1220\n", "11900.00         1195\n", "12100.00         1193\n", "1350.00          1193\n", "85000.00         1173\n", "13400.00         1164\n", "15700.00         1160\n", "17800.00         1132\n", "15100.00         1132\n", "7250.00          1127\n", "17200.00         1122\n", "6250.00          1121\n", "9750.00          1108\n", "55000.00         1106\n", "15400.00         1099\n", "16300.00         1097\n", "16600.00         1079\n", "16700.00         1077\n", "15900.00         1064\n", "18900.00         1060\n", "1650.00          1050\n", "16900.00         1042\n", "18600.00         1042\n", "8750.00          1041\n", "13100.00         1032\n", "18200.00         1027\n", "6750.00          1010\n", "17300.00         1009\n", "17600.00         1002\n", "17900.00         1000\n", "16400.00          991\n", "11750.00          991\n", "12750.00          984\n", "1850.00           967\n", "5750.00           961\n", "38500.00          958\n", "95000.00          958\n", "14100.00          952\n", "19800.00          950\n", "15750.00          944\n", "22200.00          939\n", "19900.00          938\n", "13750.00          938\n", "1550.00           938\n", "11250.00          937\n", "18700.00          933\n", "18800.00          932\n", "17700.00          926\n", "12975.00          925\n", "10975.00          924\n", "19200.00          912\n", "17400.00          907\n", "14250.00          902\n", "16100.00          895\n", "5975.00           893\n", "9950.00           879\n", "10750.00          875\n", "18300.00          875\n", "1150.00           872\n", "13975.00          871\n", "7750.00           868\n", "14750.00          858\n", "20200.00          858\n", "8250.00           856\n", "13250.00          851\n", "15550.00          850\n", "20100.00          849\n", "13650.00          843\n", "175000.00         841\n", "9850.00           837\n", "21200.00          837\n", "10550.00          837\n", "12250.00          835\n", "18400.00          830\n", "6975.00           830\n", "11975.00          824\n", "11350.00          822\n", "2550.00           822\n", "10250.00          818\n", "3850.00           818\n", "17100.00          816\n", "19600.00          815\n", "6050.00           810\n", "10875.00          805\n", "11550.00          804\n", "2650.00           803\n", "11650.00          797\n", "19700.00          795\n", "12550.00          795\n", "13625.00          793\n", "6650.00           792\n", "10950.00          791\n", "13350.00          785\n", "3050.00           784\n", "9875.00           777\n", "15975.00          777\n", "36500.00          775\n", "14425.00          771\n", "10525.00          769\n", "13850.00          764\n", "10650.00          763\n", "14475.00          762\n", "12325.00          762\n", "12350.00          761\n", "9250.00           760\n", "17975.00          759\n", "8975.00           758\n", "14450.00          755\n", "2850.00           754\n", "16750.00          753\n", "11775.00          752\n", "7975.00           750\n", "14350.00          750\n", "12475.00          747\n", "12650.00          745\n", "19300.00          745\n", "15350.00          741\n", "18750.00          741\n", "12850.00          738\n", "13550.00          737\n", "14850.00          736\n", "3325.00           736\n", "11475.00          734\n", "11450.00          734\n", "14650.00          730\n", "14950.00          729\n", "15250.00          729\n", "1450.00           728\n", "13950.00          728\n", "18100.00          728\n", "10850.00          728\n", "14625.00          727\n", "21300.00          725\n", "19400.00          725\n", "14125.00          725\n", "11325.00          722\n", "11675.00          721\n", "20600.00          720\n", "13575.00          719\n", "13875.00          717\n", "6850.00           717\n", "17750.00          717\n", "4950.00           716\n", "11125.00          715\n", "12950.00          714\n", "20400.00          713\n", "7775.00           713\n", "16250.00          712\n", "12375.00          712\n", "8550.00           712\n", "12575.00          711\n", "16975.00          711\n", "14150.00          710\n", "9525.00           710\n", "15150.00          708\n", "13275.00          708\n", "20800.00          708\n", "11850.00          707\n", "14550.00          706\n", "13725.00          705\n", "11225.00          705\n", "14175.00          702\n", "15125.00          701\n", "10350.00          701\n", "3550.00           700\n", "17250.00          700\n", "10825.00          697\n", "12050.00          696\n", "11575.00          695\n", "13675.00          693\n", "11425.00          692\n", "13775.00          692\n", "10075.00          692\n", "12450.00          691\n", "12275.00          690\n", "12150.00          690\n", "21600.00          688\n", "11150.00          686\n", "13450.00          686\n", "24975.00          685\n", "12775.00          685\n", "14525.00          684\n", "12675.00          683\n", "13475.00          683\n", "15650.00          683\n", "5050.00           683\n", "13225.00          682\n", "12425.00          682\n", "10675.00          681\n", "13175.00          680\n", "10775.00          680\n", "13125.00          680\n", "15075.00          680\n", "4550.00           680\n", "12825.00          679\n", "11625.00          677\n", "14225.00          677\n", "14875.00          677\n", "16850.00          676\n", "6550.00           676\n", "15050.00          676\n", "14375.00          675\n", "3150.00           675\n", "14725.00          675\n", "5650.00           675\n", "13325.00          674\n", "10450.00          673\n", "2975.00           672\n", "3650.00           672\n", "10425.00          671\n", "15850.00          671\n", "7550.00           670\n", "29975.00          668\n", "15175.00          668\n", "16350.00          668\n", "15225.00          666\n", "5350.00           666\n", "11875.00          666\n", "15525.00          665\n", "15625.00          665\n", "10575.00          664\n", "7850.00           664\n", "10050.00          664\n", "12225.00          663\n", "12875.00          663\n", "13150.00          663\n", "12725.00          662\n", "7650.00           662\n", "13050.00          661\n", "7350.00           661\n", "19100.00          659\n", "6350.00           659\n", "14775.00          658\n", "4850.00           657\n", "10150.00          657\n", "14275.00          657\n", "11725.00          656\n", "11025.00          656\n", "8850.00           656\n", "15575.00          655\n", "5850.00           655\n", "13525.00          655\n", "15675.00          655\n", "14575.00          653\n", "13925.00          653\n", "15775.00          652\n", "39500.00          652\n", "9775.00           652\n", "14075.00          651\n", "11050.00          651\n", "2350.00           651\n", "8875.00           651\n", "16775.00          649\n", "22300.00          648\n", "13375.00          647\n", "1025.00           647\n", "16550.00          646\n", "20300.00          645\n", "3450.00           645\n", "13075.00          644\n", "13825.00          644\n", "12525.00          643\n", "17150.00          642\n", "11950.00          642\n", "4650.00           642\n", "12125.00          642\n", "20700.00          642\n", "18250.00          641\n", "15325.00          641\n", "13425.00          641\n", "12625.00          641\n", "15275.00          641\n", "10475.00          641\n", "22400.00          639\n", "5450.00           639\n", "16225.00          638\n", "10325.00          637\n", "5875.00           636\n", "14825.00          636\n", "12025.00          635\n", "16625.00          635\n", "17850.00          634\n", "9550.00           633\n", "15875.00          632\n", "10625.00          632\n", "14050.00          632\n", "3350.00           631\n", "16450.00          631\n", "16650.00          631\n", "16875.00          631\n", "16050.00          631\n", "15025.00          630\n", "11275.00          629\n", "17350.00          628\n", "14325.00          628\n", "11825.00          628\n", "10375.00          627\n", "15450.00          626\n", "11925.00          626\n", "16150.00          626\n", "11375.00          626\n", "17550.00          625\n", "10725.00          623\n", "16675.00          621\n", "12175.00          620\n", "16275.00          620\n", "12925.00          620\n", "18975.00          618\n", "15950.00          617\n", "15475.00          616\n", "10225.00          616\n", "14925.00          616\n", "16525.00          615\n", "18650.00          615\n", "6575.00           613\n", "14675.00          613\n", "22100.00          612\n", "11175.00          612\n", "16425.00          609\n", "4350.00           608\n", "9650.00           607\n", "21800.00          607\n", "13025.00          607\n", "11525.00          606\n", "14025.00          606\n", "17025.00          606\n", "19750.00          605\n", "19850.00          605\n", "7150.00           604\n", "20900.00          602\n", "10275.00          602\n", "6150.00           602\n", "17325.00          602\n", "18550.00          601\n", "10175.00          601\n", "21400.00          601\n", "1950.00           601\n", "18350.00          601\n", "22600.00          601\n", "8950.00           599\n", "15375.00          599\n", "17575.00          599\n", "10925.00          598\n", "15725.00          598\n", "17050.00          597\n", "5275.00           597\n", "17225.00          597\n", "10125.00          596\n", "9925.00           596\n", "8350.00           595\n", "6950.00           595\n", "19250.00          595\n", "9350.00           595\n", "9825.00           594\n", "11075.00          594\n", "6875.00           594\n", "15425.00          594\n", "16075.00          594\n", "12075.00          593\n", "17650.00          593\n", "2150.00           590\n", "16950.00          590\n", "25600.00          589\n", "17275.00          589\n", "9450.00           588\n", "16325.00          586\n", "8650.00           586\n", "17825.00          586\n", "7050.00           585\n", "17075.00          585\n", "16125.00          585\n", "17875.00          584\n", "16825.00          582\n", "5775.00           581\n", "7475.00           581\n", "22800.00          581\n", "7450.00           581\n", "17775.00          581\n", "8050.00           580\n", "17475.00          580\n", "17625.00          579\n", "15825.00          578\n", "17450.00          578\n", "15925.00          577\n", "5950.00           577\n", "3950.00           577\n", "18150.00          577\n", "16375.00          575\n", "3775.00           574\n", "22700.00          574\n", "16175.00          574\n", "17175.00          573\n", "16725.00          572\n", "9050.00           572\n", "17675.00          571\n", "6325.00           571\n", "23400.00          570\n", "18875.00          570\n", "24900.00          570\n", "21100.00          569\n", "17125.00          568\n", "18450.00          568\n", "16925.00          568\n", "18125.00          568\n", "18325.00          568\n", "21700.00          567\n", "18850.00          567\n", "2450.00           566\n", "16475.00          564\n", "4450.00           564\n", "16025.00          563\n", "2950.00           563\n", "23800.00          561\n", "3575.00           561\n", "7275.00           561\n", "18050.00          560\n", "9575.00           560\n", "8775.00           560\n", "16575.00          559\n", "17425.00          558\n", "5675.00           558\n", "5150.00           558\n", "8150.00           558\n", "4425.00           556\n", "25200.00          555\n", "4150.00           555\n", "10025.00          554\n", "6525.00           553\n", "4875.00           553\n", "5075.00           552\n", "2050.00           550\n", "6225.00           550\n", "18175.00          550\n", "6775.00           549\n", "6475.00           548\n", "19050.00          547\n", "9625.00           547\n", "9275.00           546\n", "9150.00           544\n", "19950.00          544\n", "5725.00           544\n", "7675.00           543\n", "23900.00          543\n", "7575.00           542\n", "17375.00          540\n", "3875.00           540\n", "6075.00           539\n", "22900.00          538\n", "6175.00           538\n", "34975.00          538\n", "18725.00          537\n", "8450.00           537\n", "17725.00          536\n", "19275.00          535\n", "23200.00          535\n", "18525.00          534\n", "18950.00          534\n", "17925.00          534\n", "2175.00           531\n", "7875.00           530\n", "21900.00          530\n", "17950.00          530\n", "20250.00          530\n", "19625.00          530\n", "18475.00          529\n", "5475.00           529\n", "5575.00           529\n", "5325.00           527\n", "6675.00           527\n", "18625.00          525\n", "18575.00          525\n", "23300.00          525\n", "18825.00          524\n", "18675.00          523\n", "1075.00           523\n", "18025.00          522\n", "8225.00           522\n", "17525.00          521\n", "7825.00           521\n", "23700.00          521\n", "6375.00           520\n", "7425.00           520\n", "18775.00          520\n", "6275.00           519\n", "19650.00          519\n", "23750.00          517\n", "6725.00           517\n", "18225.00          517\n", "5525.00           517\n", "4050.00           516\n", "6125.00           516\n", "19025.00          516\n", "6450.00           515\n", "9075.00           514\n", "5375.00           514\n", "7625.00           513\n", "24200.00          512\n", "19450.00          512\n", "9675.00           512\n", "5025.00           511\n", "18075.00          510\n", "2575.00           509\n", "9725.00           509\n", "7525.00           508\n", "5225.00           508\n", "19075.00          508\n", "2875.00           508\n", "18425.00          508\n", "7325.00           507\n", "1975.00           507\n", "9475.00           506\n", "8575.00           506\n", "3675.00           505\n", "19475.00          505\n", "20750.00          505\n", "19550.00          504\n", "4775.00           504\n", "8325.00           503\n", "6625.00           503\n", "5625.00           503\n", "4675.00           502\n", "7025.00           500\n", "19175.00          500\n", "21350.00          499\n", "18375.00          498\n", "18275.00          497\n", "21550.00          496\n", "24600.00          496\n", "20050.00          496\n", "19725.00          495\n", "7225.00           494\n", "9375.00           493\n", "8125.00           493\n", "19775.00          493\n", "8525.00           492\n", "8825.00           491\n", "19225.00          491\n", "24300.00          491\n", "8475.00           491\n", "19525.00          491\n", "23100.00          491\n", "23600.00          490\n", "20450.00          490\n", "8075.00           490\n", "5175.00           489\n", "19675.00          488\n", "19150.00          487\n", "8625.00           487\n", "5425.00           487\n", "1875.00           487\n", "7950.00           487\n", "19425.00          487\n", "3475.00           485\n", "6825.00           485\n", "1275.00           485\n", "1225.00           484\n", "9025.00           484\n", "7175.00           484\n", "18925.00          484\n", "20975.00          484\n", "3075.00           484\n", "8725.00           484\n", "1575.00           483\n", "5825.00           482\n", "6925.00           482\n", "2475.00           481\n", "20150.00          480\n", "8425.00           480\n", "29900.00          480\n", "24700.00          480\n", "7375.00           478\n", "25300.00          477\n", "20075.00          476\n", "6425.00           476\n", "4225.00           475\n", "9425.00           475\n", "22250.00          474\n", "7075.00           474\n", "7725.00           474\n", "8275.00           474\n", "21975.00          472\n", "20575.00          471\n", "19875.00          471\n", "24800.00          470\n", "20225.00          470\n", "9175.00           470\n", "9225.00           469\n", "5125.00           468\n", "4325.00           468\n", "19575.00          467\n", "25100.00          467\n", "19350.00          466\n", "8025.00           465\n", "9325.00           465\n", "20850.00          464\n", "4525.00           464\n", "25800.00          464\n", "20175.00          463\n", "21250.00          462\n", "7125.00           462\n", "8375.00           461\n", "25700.00          461\n", "20550.00          461\n", "20125.00          461\n", "19375.00          459\n", "19125.00          458\n", "2775.00           458\n", "20350.00          458\n", "25400.00          456\n", "4275.00           456\n", "19825.00          455\n", "19925.00          455\n", "26600.00          454\n", "19325.00          454\n", "8675.00           454\n", "1375.00           453\n", "4575.00           453\n", "8175.00           452\n", "3175.00           452\n", "21750.00          452\n", "8925.00           451\n", "4725.00           449\n", "26300.00          448\n", "4475.00           446\n", "21025.00          446\n", "24400.00          446\n", "20675.00          445\n", "4925.00           445\n", "21050.00          444\n", "20025.00          441\n", "3725.00           440\n", "2225.00           440\n", "21575.00          439\n", "25900.00          438\n", "130000.00         438\n", "1325.00           438\n", "20475.00          438\n", "20650.00          437\n", "22575.00          437\n", "3275.00           436\n", "24100.00          435\n", "20775.00          435\n", "21225.00          435\n", "28800.00          435\n", "20725.00          434\n", "20425.00          433\n", "23975.00          433\n", "23450.00          432\n", "21150.00          432\n", "22750.00          432\n", "4075.00           431\n", "4625.00           429\n", "21525.00          429\n", "4025.00           428\n", "180000.00         428\n", "20825.00          427\n", "4175.00           426\n", "5925.00           426\n", "2675.00           426\n", "21450.00          425\n", "9125.00           425\n", "4375.00           425\n", "20875.00          424\n", "20625.00          423\n", "22275.00          423\n", "26800.00          420\n", "21950.00          420\n", "20325.00          420\n", "3375.00           420\n", "2525.00           419\n", "22350.00          419\n", "21175.00          419\n", "21275.00          418\n", "21425.00          418\n", "7925.00           418\n", "26700.00          418\n", "21325.00          417\n", "21375.00          417\n", "21875.00          417\n", "3525.00           416\n", "22175.00          414\n", "20275.00          414\n", "20525.00          413\n", "20375.00          413\n", "2325.00           412\n", "26200.00          412\n", "20925.00          410\n", "21650.00          410\n", "21725.00          409\n", "3625.00           409\n", "21625.00          409\n", "24250.00          408\n", "3425.00           407\n", "26400.00          406\n", "21775.00          405\n", "21850.00          405\n", "22450.00          403\n", "22225.00          403\n", "22550.00          402\n", "1125.00           402\n", "4825.00           402\n", "22025.00          401\n", "2275.00           399\n", "22975.00          398\n", "22325.00          398\n", "2625.00           397\n", "23550.00          395\n", "160000.00         394\n", "23325.00          394\n", "21125.00          394\n", "22625.00          392\n", "2925.00           392\n", "21475.00          392\n", "22050.00          391\n", "21075.00          391\n", "23250.00          389\n", "25550.00          388\n", "23875.00          387\n", "1775.00           387\n", "23225.00          386\n", "22425.00          385\n", "27300.00          385\n", "2125.00           385\n", "22125.00          385\n", "4125.00           384\n", "21925.00          384\n", "21825.00          384\n", "23375.00          383\n", "22850.00          383\n", "23150.00          383\n", "28900.00          382\n", "25250.00          382\n", "21675.00          382\n", "3125.00           382\n", "23525.00          381\n", "23650.00          380\n", "20950.00          380\n", "26100.00          380\n", "27800.00          380\n", "22375.00          380\n", "23725.00          379\n", "22150.00          378\n", "23575.00          377\n", "23350.00          377\n", "22475.00          376\n", "2825.00           376\n", "3825.00           376\n", "27600.00          375\n", "22775.00          373\n", "23425.00          372\n", "1475.00           372\n", "23775.00          371\n", "26900.00          371\n", "22875.00          371\n", "23275.00          370\n", "22675.00          369\n", "2075.00           369\n", "30100.00          366\n", "26250.00          366\n", "23475.00          365\n", "2375.00           365\n", "28700.00          365\n", "2425.00           365\n", "25975.00          365\n", "27400.00          364\n", "27900.00          363\n", "23825.00          363\n", "22725.00          363\n", "24550.00          362\n", "33250.00          362\n", "22650.00          361\n", "23025.00          361\n", "22950.00          361\n", "23075.00          360\n", "24225.00          357\n", "1675.00           356\n", "23675.00          354\n", "24650.00          353\n", "22825.00          352\n", "27200.00          352\n", "29800.00          352\n", "2025.00           351\n", "1525.00           351\n", "1175.00           350\n", "23850.00          350\n", "22925.00          349\n", "24425.00          349\n", "27700.00          348\n", "24150.00          347\n", "23050.00          347\n", "24350.00          347\n", "25050.00          347\n", "2725.00           346\n", "22075.00          346\n", "28600.00          346\n", "24950.00          344\n", "24475.00          341\n", "24450.00          341\n", "22525.00          341\n", "23625.00          340\n", "3225.00           339\n", "24750.00          339\n", "23175.00          338\n", "25650.00          337\n", "28300.00          336\n", "24275.00          335\n", "1625.00           334\n", "27100.00          334\n", "23925.00          330\n", "25350.00          330\n", "24025.00          329\n", "140000.00         328\n", "24725.00          324\n", "23125.00          324\n", "24175.00          324\n", "3925.00           323\n", "30200.00          323\n", "24050.00          322\n", "28400.00          322\n", "25075.00          320\n", "24850.00          320\n", "24675.00          320\n", "26150.00          320\n", "1725.00           316\n", "24325.00          316\n", "24375.00          315\n", "25750.00          315\n", "275000.00         314\n", "25575.00          313\n", "24575.00          313\n", "23950.00          312\n", "24825.00          311\n", "225000.00         311\n", "26975.00          311\n", "28200.00          310\n", "29700.00          310\n", "500.00            309\n", "25875.00          309\n", "24125.00          309\n", "25450.00          307\n", "24075.00          305\n", "25025.00          305\n", "25675.00          304\n", "25175.00          302\n", "24775.00          300\n", "24525.00          299\n", "25225.00          297\n", "110000.00         294\n", "25150.00          293\n", "28100.00          292\n", "39975.00          292\n", "24925.00          292\n", "1825.00           292\n", "26050.00          290\n", "24625.00          290\n", "25725.00          290\n", "25525.00          289\n", "27650.00          288\n", "1425.00           288\n", "25375.00          288\n", "27250.00          287\n", "25325.00          287\n", "24875.00          286\n", "30300.00          285\n", "25475.00          284\n", "25775.00          283\n", "25275.00          283\n", "25425.00          282\n", "26350.00          281\n", "26225.00          281\n", "27750.00          281\n", "99000.00          280\n", "26650.00          279\n", "26750.00          278\n", "29600.00          278\n", "1925.00           278\n", "34900.00          277\n", "28750.00          277\n", "29200.00          276\n", "26450.00          275\n", "26725.00          275\n", "26475.00          274\n", "31200.00          274\n", "26550.00          273\n", "26525.00          273\n", "27975.00          271\n", "25950.00          270\n", "25625.00          269\n", "27350.00          268\n", "26375.00          268\n", "28975.00          267\n", "25125.00          267\n", "25850.00          266\n", "27575.00          266\n", "26325.00          265\n", "30800.00          265\n", "26625.00          264\n", "30600.00          264\n", "28350.00          263\n", "26425.00          263\n", "29400.00          263\n", "25825.00          262\n", "27775.00          261\n", "26850.00          261\n", "27675.00          261\n", "25925.00          260\n", "34200.00          260\n", "29300.00          259\n", "32300.00          258\n", "31800.00          258\n", "27325.00          257\n", "26125.00          257\n", "26025.00          256\n", "27525.00          255\n", "28250.00          255\n", "26275.00          255\n", "29100.00          254\n", "27950.00          254\n", "27825.00          254\n", "32400.00          253\n", "27550.00          252\n", "32800.00          251\n", "33600.00          251\n", "27850.00          250\n", "33200.00          250\n", "33300.00          250\n", "33800.00          249\n", "30900.00          248\n", "26175.00          247\n", "28950.00          247\n", "26575.00          246\n", "27050.00          245\n", "27075.00          244\n", "28675.00          244\n", "26675.00          243\n", "28875.00          243\n", "27450.00          243\n", "27425.00          241\n", "31600.00          241\n", "26875.00          241\n", "33400.00          240\n", "28125.00          239\n", "26825.00          238\n", "28550.00          238\n", "27475.00          236\n", "28525.00          236\n", "30400.00          236\n", "28775.00          235\n", "32600.00          233\n", "26925.00          233\n", "27375.00          232\n", "27125.00          230\n", "26075.00          230\n", "27025.00          229\n", "27725.00          228\n", "27275.00          228\n", "32700.00          228\n", "28275.00          227\n", "27150.00          227\n", "28175.00          226\n", "28825.00          226\n", "28225.00          226\n", "27225.00          226\n", "28425.00          225\n", "26775.00          225\n", "26950.00          224\n", "30700.00          223\n", "32200.00          223\n", "31700.00          222\n", "28575.00          222\n", "29750.00          222\n", "28050.00          222\n", "28475.00          220\n", "28450.00          219\n", "29125.00          219\n", "28150.00          219\n", "32900.00          218\n", "28850.00          218\n", "30250.00          216\n", "28025.00          216\n", "29250.00          216\n", "29850.00          216\n", "28650.00          216\n", "28625.00          215\n", "31900.00          213\n", "31100.00          213\n", "30325.00          213\n", "34325.00          212\n", "27175.00          211\n", "27925.00          211\n", "27625.00          210\n", "28075.00          208\n", "29675.00          207\n", "29150.00          207\n", "30075.00          206\n", "31300.00          206\n", "32100.00          205\n", "28725.00          205\n", "29275.00          205\n", "28375.00          205\n", "33900.00          204\n", "29950.00          202\n", "29225.00          202\n", "30025.00          200\n", "27875.00          200\n", "29650.00          200\n", "31400.00          200\n", "29550.00          200\n", "34800.00          200\n", "29725.00          199\n", "33700.00          199\n", "30975.00          197\n", "29425.00          195\n", "32750.00          192\n", "29625.00          192\n", "29775.00          191\n", "29075.00          191\n", "29050.00          190\n", "34600.00          188\n", "34300.00          188\n", "28925.00          187\n", "29325.00          187\n", "30225.00          187\n", "34225.00          186\n", "29375.00          185\n", "29175.00          183\n", "30425.00          183\n", "170000.00         181\n", "31150.00          181\n", "30550.00          181\n", "28325.00          180\n", "29575.00          180\n", "31250.00          180\n", "39900.00          180\n", "31475.00          179\n", "32250.00          179\n", "30750.00          179\n", "31525.00          178\n", "29350.00          178\n", "29825.00          177\n", "30050.00          177\n", "32975.00          176\n", "30525.00          176\n", "30150.00          176\n", "32550.00          175\n", "31975.00          174\n", "31075.00          174\n", "32450.00          174\n", "31550.00          172\n", "30875.00          172\n", "29925.00          172\n", "30575.00          171\n", "29525.00          171\n", "135000.00         170\n", "32425.00          170\n", "30125.00          170\n", "32150.00          170\n", "33975.00          170\n", "34700.00          169\n", "34550.00          169\n", "34400.00          168\n", "31050.00          168\n", "31225.00          167\n", "30650.00          166\n", "34100.00          166\n", "42000.00          166\n", "31750.00          166\n", "29025.00          166\n", "32650.00          165\n", "30850.00          165\n", "30725.00          164\n", "30350.00          163\n", "29475.00          162\n", "31825.00          162\n", "32275.00          161\n", "30450.00          161\n", "30825.00          160\n", "29450.00          160\n", "29875.00          160\n", "33100.00          160\n", "31275.00          160\n", "32875.00          159\n", "30375.00          159\n", "30175.00          157\n", "31025.00          156\n", "33750.00          156\n", "31350.00          156\n", "31650.00          156\n", "31850.00          155\n", "30950.00          154\n", "33575.00          154\n", "30475.00          153\n", "30625.00          153\n", "32575.00          153\n", "30275.00          153\n", "32350.00          152\n", "30675.00          152\n", "31775.00          151\n", "32325.00          150\n", "31725.00          149\n", "31450.00          149\n", "31575.00          149\n", "34525.00          148\n", "33425.00          148\n", "31625.00          148\n", "30925.00          147\n", "34350.00          147\n", "33150.00          146\n", "31375.00          146\n", "32125.00          146\n", "32475.00          145\n", "31125.00          144\n", "31175.00          143\n", "32525.00          143\n", "33125.00          143\n", "48000.00          143\n", "33550.00          139\n", "34850.00          139\n", "33850.00          138\n", "35100.00          138\n", "30775.00          138\n", "32850.00          138\n", "35200.00          138\n", "33950.00          138\n", "32775.00          136\n", "34425.00          135\n", "33875.00          135\n", "31325.00          135\n", "33025.00          135\n", "32225.00          134\n", "33825.00          134\n", "32075.00          133\n", "34250.00          133\n", "34625.00          133\n", "32375.00          133\n", "220000.00         133\n", "31925.00          133\n", "32625.00          132\n", "33650.00          132\n", "33525.00          132\n", "32050.00          132\n", "32950.00          132\n", "33350.00          131\n", "32175.00          131\n", "280000.00         130\n", "32725.00          129\n", "34650.00          129\n", "34450.00          129\n", "32675.00          129\n", "34375.00          129\n", "31950.00          129\n", "34125.00          129\n", "34575.00          129\n", "190000.00         127\n", "31675.00          127\n", "33675.00          127\n", "32925.00          126\n", "34950.00          126\n", "33175.00          126\n", "31875.00          126\n", "33725.00          126\n", "34675.00          126\n", "33450.00          126\n", "115000.00         125\n", "33225.00          125\n", "31425.00          125\n", "34175.00          123\n", "34050.00          123\n", "33625.00          123\n", "34075.00          122\n", "33775.00          120\n", "34925.00          120\n", "33050.00          120\n", "34750.00          119\n", "35700.00          119\n", "33275.00          119\n", "32825.00          118\n", "34275.00          117\n", "33475.00          117\n", "32025.00          117\n", "34150.00          115\n", "33375.00          114\n", "34875.00          113\n", "35600.00          111\n", "34775.00          110\n", "35800.00          108\n", "35400.00          108\n", "33075.00          107\n", "34025.00          107\n", "34725.00          107\n", "33925.00          106\n", "36800.00          105\n", "49000.00          104\n", "37800.00           96\n", "34475.00           96\n", "36200.00           96\n", "165000.00          96\n", "35300.00           94\n", "36900.00           94\n", "240000.00          93\n", "34825.00           92\n", "37200.00           91\n", "37900.00           91\n", "36400.00           90\n", "36600.00           90\n", "36700.00           89\n", "35450.00           89\n", "38900.00           86\n", "185000.00          86\n", "260000.00          86\n", "35900.00           86\n", "37600.00           85\n", "37400.00           85\n", "52000.00           84\n", "39800.00           82\n", "35550.00           82\n", "35750.00           81\n", "38600.00           80\n", "36300.00           80\n", "38800.00           79\n", "35650.00           78\n", "35875.00           78\n", "38400.00           77\n", "35425.00           77\n", "35975.00           77\n", "230000.00          77\n", "38700.00           76\n", "35225.00           76\n", "35350.00           75\n", "35725.00           75\n", "36750.00           75\n", "44000.00           75\n", "35025.00           74\n", "105000.00          73\n", "35250.00           72\n", "38750.00           71\n", "35575.00           71\n", "39700.00           71\n", "36975.00           70\n", "35675.00           70\n", "35125.00           70\n", "210000.00          70\n", "36250.00           69\n", "37975.00           69\n", "35175.00           69\n", "35150.00           68\n", "35950.00           68\n", "46000.00           68\n", "36550.00           68\n", "35325.00           67\n", "37300.00           67\n", "36225.00           67\n", "39875.00           67\n", "98000.00           66\n", "36350.00           66\n", "37700.00           66\n", "36100.00           66\n", "37750.00           65\n", "36125.00           65\n", "145000.00          65\n", "35475.00           65\n", "35275.00           64\n", "36725.00           64\n", "35625.00           64\n", "39600.00           64\n", "36525.00           64\n", "35375.00           63\n", "35925.00           63\n", "37100.00           63\n", "43000.00           63\n", "37025.00           62\n", "36075.00           62\n", "36150.00           62\n", "35525.00           62\n", "36825.00           62\n", "37225.00           61\n", "37575.00           61\n", "39200.00           61\n", "36950.00           61\n", "35850.00           60\n", "37250.00           60\n", "36775.00           60\n", "36650.00           60\n", "47000.00           60\n", "35825.00           59\n", "38200.00           59\n", "270000.00          59\n", "36575.00           59\n", "37725.00           59\n", "72000.00           59\n", "35075.00           58\n", "38300.00           58\n", "38975.00           58\n", "37850.00           57\n", "38225.00           57\n", "36175.00           57\n", "38250.00           57\n", "37625.00           56\n", "37325.00           56\n", "35050.00           56\n", "37125.00           56\n", "36875.00           55\n", "41000.00           55\n", "68000.00           55\n", "36450.00           54\n", "37475.00           54\n", "37525.00           54\n", "37550.00           54\n", "39300.00           53\n", "37650.00           53\n", "37050.00           53\n", "56000.00           53\n", "36325.00           52\n", "38525.00           52\n", "37775.00           52\n", "39400.00           52\n", "36475.00           52\n", "36675.00           52\n", "37150.00           52\n", "36375.00           52\n", "290000.00          51\n", "35775.00           51\n", "36625.00           51\n", "38550.00           51\n", "36425.00           51\n", "38450.00           51\n", "38350.00           51\n", "36925.00           50\n", "38125.00           50\n", "39950.00           50\n", "37175.00           50\n", "36275.00           50\n", "38475.00           50\n", "37450.00           49\n", "39775.00           49\n", "39100.00           48\n", "38650.00           48\n", "37675.00           48\n", "37275.00           48\n", "37825.00           47\n", "37075.00           47\n", "39475.00           47\n", "38100.00           47\n", "38075.00           47\n", "39275.00           46\n", "39450.00           46\n", "36050.00           46\n", "38675.00           46\n", "38775.00           46\n", "38375.00           45\n", "195000.00          45\n", "53000.00           45\n", "37375.00           45\n", "37875.00           44\n", "38425.00           44\n", "62000.00           44\n", "36850.00           44\n", "39025.00           44\n", "38050.00           44\n", "38150.00           44\n", "38925.00           43\n", "58000.00           43\n", "39575.00           43\n", "88000.00           42\n", "800.00             42\n", "39750.00           42\n", "38875.00           42\n", "57000.00           41\n", "38725.00           41\n", "39650.00           41\n", "37425.00           41\n", "39675.00           41\n", "36025.00           41\n", "39725.00           40\n", "39925.00           40\n", "38825.00           40\n", "78000.00           40\n", "38575.00           40\n", "285000.00          39\n", "37350.00           39\n", "37950.00           39\n", "39250.00           39\n", "40375.00           38\n", "38950.00           38\n", "39225.00           37\n", "54000.00           37\n", "39175.00           37\n", "155000.00          37\n", "39325.00           37\n", "39050.00           37\n", "38175.00           36\n", "41700.00           36\n", "38275.00           35\n", "38325.00           35\n", "42500.00           35\n", "40175.00           35\n", "39150.00           35\n", "39825.00           35\n", "38025.00           34\n", "39550.00           34\n", "215000.00          34\n", "39350.00           33\n", "64000.00           32\n", "39075.00           32\n", "40625.00           32\n", "96000.00           31\n", "265000.00          31\n", "38625.00           30\n", "39850.00           30\n", "39125.00           30\n", "40225.00           30\n", "38850.00           30\n", "39625.00           29\n", "39525.00           29\n", "39375.00           29\n", "40125.00           29\n", "76000.00           29\n", "40275.00           29\n", "45500.00           28\n", "87000.00           28\n", "66000.00           28\n", "600.00             28\n", "41500.00           28\n", "40675.00           28\n", "40250.00           28\n", "295000.00          28\n", "40050.00           28\n", "40525.00           28\n", "235000.00          27\n", "245000.00          27\n", "51000.00           27\n", "40025.00           27\n", "92000.00           27\n", "40100.00           27\n", "41075.00           26\n", "40400.00           26\n", "43500.00           26\n", "40300.00           26\n", "45400.00           26\n", "37925.00           25\n", "40875.00           25\n", "42550.00           25\n", "41025.00           25\n", "40825.00           25\n", "67000.00           25\n", "44600.00           24\n", "40600.00           24\n", "40500.00           24\n", "97000.00           24\n", "700.00             24\n", "41100.00           24\n", "43175.00           24\n", "43200.00           24\n", "42775.00           23\n", "40075.00           23\n", "74000.00           23\n", "43100.00           23\n", "42375.00           23\n", "59000.00           23\n", "63000.00           22\n", "77000.00           22\n", "41200.00           22\n", "40425.00           22\n", "42600.00           22\n", "41800.00           22\n", "40975.00           22\n", "41300.00           22\n", "40450.00           22\n", "43675.00           21\n", "43250.00           21\n", "42200.00           21\n", "39425.00           21\n", "79000.00           21\n", "42575.00           21\n", "42525.00           21\n", "40150.00           21\n", "40325.00           21\n", "40350.00           21\n", "40800.00           21\n", "44900.00           21\n", "43750.00           21\n", "44375.00           21\n", "41675.00           21\n", "41125.00           20\n", "41825.00           20\n", "41275.00           20\n", "43725.00           20\n", "82000.00           20\n", "47500.00           20\n", "43625.00           20\n", "42875.00           20\n", "41850.00           20\n", "43375.00           20\n", "42100.00           19\n", "89000.00           19\n", "41150.00           19\n", "41425.00           19\n", "43575.00           19\n", "41925.00           19\n", "42450.00           19\n", "41650.00           19\n", "41975.00           19\n", "42400.00           19\n", "43075.00           18\n", "205000.00          18\n", "41175.00           18\n", "42825.00           18\n", "41475.00           18\n", "40550.00           18\n", "44875.00           18\n", "41900.00           18\n", "41450.00           18\n", "41600.00           18\n", "40475.00           18\n", "73000.00           18\n", "45800.00           18\n", "43475.00           18\n", "42150.00           18\n", "40575.00           18\n", "42300.00           18\n", "41225.00           17\n", "42175.00           17\n", "43325.00           17\n", "41775.00           17\n", "40850.00           17\n", "40750.00           17\n", "41550.00           17\n", "42250.00           17\n", "69000.00           17\n", "42350.00           17\n", "41625.00           17\n", "45575.00           17\n", "46075.00           17\n", "42225.00           17\n", "41400.00           17\n", "41750.00           16\n", "44700.00           16\n", "40775.00           16\n", "40725.00           16\n", "44850.00           16\n", "45450.00           16\n", "43225.00           16\n", "44575.00           16\n", "42950.00           16\n", "83000.00           16\n", "42700.00           16\n", "41575.00           16\n", "44475.00           16\n", "44025.00           16\n", "43150.00           16\n", "42275.00           16\n", "45075.00           16\n", "43525.00           16\n", "43400.00           16\n", "43125.00           16\n", "43350.00           16\n", "40200.00           16\n", "44200.00           16\n", "44400.00           16\n", "45050.00           16\n", "42050.00           16\n", "41250.00           16\n", "42750.00           16\n", "42125.00           15\n", "42425.00           15\n", "47300.00           15\n", "43925.00           15\n", "44950.00           15\n", "44300.00           15\n", "41375.00           15\n", "43300.00           15\n", "44550.00           15\n", "47800.00           15\n", "61000.00           15\n", "44775.00           15\n", "46100.00           15\n", "47950.00           15\n", "45975.00           15\n", "44050.00           15\n", "40950.00           15\n", "43600.00           15\n", "45875.00           15\n", "42725.00           15\n", "40925.00           15\n", "81000.00           15\n", "94000.00           15\n", "44425.00           15\n", "42475.00           15\n", "42675.00           15\n", "40900.00           15\n", "41725.00           15\n", "45625.00           15\n", "44825.00           14\n", "43825.00           14\n", "45850.00           14\n", "49525.00           14\n", "86000.00           14\n", "255000.00          14\n", "42975.00           14\n", "42800.00           14\n", "40650.00           14\n", "43800.00           14\n", "44800.00           14\n", "48850.00           14\n", "44625.00           14\n", "48500.00           14\n", "42850.00           14\n", "46575.00           14\n", "41875.00           14\n", "40700.00           14\n", "43900.00           14\n", "42650.00           14\n", "45900.00           14\n", "43850.00           14\n", "41525.00           13\n", "42900.00           13\n", "45525.00           13\n", "43875.00           13\n", "99500.00           13\n", "46875.00           13\n", "43775.00           13\n", "43025.00           13\n", "47275.00           13\n", "48400.00           13\n", "45700.00           13\n", "43450.00           13\n", "50100.00           13\n", "44500.00           13\n", "51675.00           13\n", "46125.00           13\n", "43700.00           13\n", "42025.00           13\n", "45950.00           13\n", "44675.00           13\n", "47825.00           13\n", "47875.00           13\n", "43950.00           13\n", "149000.00          13\n", "41950.00           13\n", "45225.00           13\n", "44925.00           13\n", "49900.00           13\n", "127000.00          12\n", "49125.00           12\n", "44725.00           12\n", "45925.00           12\n", "49375.00           12\n", "45200.00           12\n", "46550.00           12\n", "45675.00           12\n", "42925.00           12\n", "42325.00           12\n", "43650.00           12\n", "49500.00           12\n", "46375.00           12\n", "43275.00           12\n", "99900.00           12\n", "44250.00           12\n", "47725.00           12\n", "47625.00           12\n", "49425.00           12\n", "52975.00           12\n", "49400.00           12\n", "44150.00           12\n", "47675.00           12\n", "46975.00           12\n", "44525.00           12\n", "44325.00           12\n", "43050.00           12\n", "43425.00           12\n", "48375.00           12\n", "46400.00           12\n", "44275.00           12\n", "45825.00           12\n", "48125.00           12\n", "45100.00           12\n", "41325.00           12\n", "44350.00           12\n", "46950.00           11\n", "112000.00          11\n", "44450.00           11\n", "46200.00           11\n", "45425.00           11\n", "44750.00           11\n", "45300.00           11\n", "48150.00           11\n", "44975.00           11\n", "46350.00           11\n", "45750.00           11\n", "49025.00           11\n", "46625.00           11\n", "49250.00           11\n", "84000.00           11\n", "41350.00           11\n", "46250.00           11\n", "53100.00           11\n", "51800.00           11\n", "46600.00           11\n", "46775.00           11\n", "42075.00           11\n", "46700.00           11\n", "50200.00           11\n", "44650.00           11\n", "47100.00           11\n", "45275.00           11\n", "51200.00           10\n", "46025.00           10\n", "47550.00           10\n", "46425.00           10\n", "48350.00           10\n", "49800.00           10\n", "50050.00           10\n", "50500.00           10\n", "47200.00           10\n", "49275.00           10\n", "45650.00           10\n", "47350.00           10\n", "47025.00           10\n", "47075.00           10\n", "43975.00           10\n", "900.00             10\n", "55675.00           10\n", "71000.00           10\n", "48300.00           10\n", "138000.00          10\n", "50300.00           10\n", "47050.00           10\n", "45125.00           10\n", "47175.00           10\n", "46450.00           10\n", "45150.00           10\n", "46150.00           10\n", "47400.00           10\n", "47925.00           10\n", "47575.00           10\n", "46725.00           10\n", "91000.00           10\n", "45775.00            9\n", "299000.00           9\n", "47450.00            9\n", "48050.00            9\n", "49225.00            9\n", "45350.00            9\n", "44225.00            9\n", "44125.00            9\n", "48450.00            9\n", "45250.00            9\n", "47900.00            9\n", "49975.00            9\n", "93000.00            9\n", "46325.00            9\n", "44100.00            9\n", "47475.00            9\n", "51500.00            9\n", "158000.00           9\n", "49550.00            9\n", "45175.00            9\n", "46175.00            9\n", "47850.00            9\n", "46500.00            9\n", "46225.00            9\n", "48925.00            9\n", "51050.00            9\n", "47425.00            9\n", "41050.00            9\n", "48900.00            9\n", "44075.00            9\n", "46275.00            9\n", "50125.00            9\n", "45475.00            9\n", "48800.00            9\n", "53400.00            9\n", "48100.00            9\n", "50700.00            9\n", "57500.00            8\n", "48700.00            8\n", "50225.00            8\n", "178000.00           8\n", "144000.00           8\n", "45725.00            8\n", "46925.00            8\n", "51650.00            8\n", "48475.00            8\n", "47650.00            8\n", "50275.00            8\n", "47525.00            8\n", "48275.00            8\n", "169000.00           8\n", "46650.00            8\n", "51875.00            8\n", "126000.00           8\n", "51350.00            8\n", "51475.00            8\n", "53675.00            8\n", "49200.00            8\n", "54750.00            8\n", "50550.00            8\n", "48875.00            8\n", "44175.00            8\n", "129000.00           8\n", "45375.00            8\n", "525.00              8\n", "137000.00           8\n", "52825.00            8\n", "48175.00            8\n", "49700.00            8\n", "50775.00            8\n", "52200.00            8\n", "49875.00            8\n", "51325.00            8\n", "45325.00            8\n", "47150.00            8\n", "52500.00            8\n", "50675.00            8\n", "49150.00            7\n", "128000.00           7\n", "50975.00            7\n", "51025.00            7\n", "48075.00            7\n", "45600.00            7\n", "43550.00            7\n", "52850.00            7\n", "67500.00            7\n", "52375.00            7\n", "51925.00            7\n", "52300.00            7\n", "51625.00            7\n", "50350.00            7\n", "47325.00            7\n", "132000.00           7\n", "154000.00           7\n", "49925.00            7\n", "48425.00            7\n", "52325.00            7\n", "50750.00            7\n", "46800.00            7\n", "46300.00            7\n", "50325.00            7\n", "52050.00            7\n", "52450.00            7\n", "52625.00            7\n", "49050.00            7\n", "46750.00            7\n", "48200.00            7\n", "54500.00            7\n", "47125.00            7\n", "50400.00            7\n", "53375.00            7\n", "56400.00            7\n", "47375.00            7\n", "49175.00            7\n", "51975.00            7\n", "46525.00            7\n", "49100.00            7\n", "52775.00            7\n", "166000.00           7\n", "52425.00            7\n", "53525.00            6\n", "168000.00           6\n", "103000.00           6\n", "55300.00            6\n", "48625.00            6\n", "49075.00            6\n", "54325.00            6\n", "42625.00            6\n", "45550.00            6\n", "52225.00            6\n", "52525.00            6\n", "52025.00            6\n", "53200.00            6\n", "46675.00            6\n", "49575.00            6\n", "750.00              6\n", "53150.00            6\n", "97500.00            6\n", "54650.00            6\n", "54125.00            6\n", "50175.00            6\n", "52150.00            6\n", "256000.00           6\n", "51250.00            6\n", "9999.00             6\n", "48975.00            6\n", "51450.00            6\n", "51725.00            6\n", "47600.00            6\n", "47750.00            6\n", "49450.00            6\n", "49350.00            6\n", "51100.00            6\n", "48600.00            6\n", "48650.00            6\n", "56800.00            6\n", "52650.00            6\n", "213000.00           6\n", "199000.00           6\n", "49600.00            6\n", "50950.00            6\n", "50575.00            6\n", "50650.00            6\n", "47250.00            6\n", "58950.00            6\n", "50725.00            6\n", "47775.00            6\n", "139000.00           6\n", "55500.00            6\n", "52950.00            6\n", "54575.00            6\n", "49650.00            6\n", "45025.00            6\n", "50825.00            6\n", "48825.00            6\n", "50900.00            6\n", "53475.00            6\n", "52575.00            6\n", "49950.00            6\n", "54250.00            5\n", "50525.00            5\n", "55900.00            5\n", "53750.00            5\n", "975.00              5\n", "55025.00            5\n", "46050.00            5\n", "49300.00            5\n", "56475.00            5\n", "53950.00            5\n", "111000.00           5\n", "52125.00            5\n", "172000.00           5\n", "119000.00           5\n", "48950.00            5\n", "46825.00            5\n", "187000.00           5\n", "48225.00            5\n", "109000.00           5\n", "48325.00            5\n", "56500.00            5\n", "60825.00            5\n", "53550.00            5\n", "53575.00            5\n", "51850.00            5\n", "116000.00           5\n", "54550.00            5\n", "57025.00            5\n", "54400.00            5\n", "51525.00            5\n", "53625.00            5\n", "62500.00            5\n", "53325.00            5\n", "60300.00            5\n", "55225.00            5\n", "51150.00            5\n", "46850.00            5\n", "48025.00            5\n", "54475.00            5\n", "64500.00            5\n", "48525.00            5\n", "52675.00            5\n", "59100.00            5\n", "47700.00            5\n", "47975.00            5\n", "52550.00            5\n", "108000.00           5\n", "50875.00            5\n", "59800.00            5\n", "159000.00           5\n", "46900.00            5\n", "288000.00           5\n", "59725.00            5\n", "50250.00            5\n", "53250.00            5\n", "49675.00            5\n", "53175.00            5\n", "54525.00            5\n", "101000.00           5\n", "167000.00           5\n", "850.00              5\n", "147000.00           5\n", "114000.00           5\n", "100100.00           5\n", "55625.00            5\n", "164000.00           5\n", "74900.00            5\n", "575.00              5\n", "48550.00            5\n", "65500.00            5\n", "650.00              5\n", "113000.00           5\n", "122000.00           5\n", "300.00              5\n", "47225.00            4\n", "193000.00           4\n", "49750.00            4\n", "98500.00            4\n", "57250.00            4\n", "78500.00            4\n", "58600.00            4\n", "90800.00            4\n", "86500.00            4\n", "48725.00            4\n", "49775.00            4\n", "88500.00            4\n", "118000.00           4\n", "104000.00           4\n", "55350.00            4\n", "825.00              4\n", "106000.00           4\n", "54300.00            4\n", "52800.00            4\n", "63025.00            4\n", "61300.00            4\n", "51125.00            4\n", "53050.00            4\n", "51775.00            4\n", "49325.00            4\n", "50475.00            4\n", "55550.00            4\n", "54150.00            4\n", "53600.00            4\n", "65175.00            4\n", "58825.00            4\n", "87500.00            4\n", "134000.00           4\n", "148000.00           4\n", "50075.00            4\n", "200.00              4\n", "299900.00           4\n", "52275.00            4\n", "50450.00            4\n", "53850.00            4\n", "152000.00           4\n", "57125.00            4\n", "54425.00            4\n", "58500.00            4\n", "52925.00            4\n", "59400.00            4\n", "51075.00            4\n", "52750.00            4\n", "54100.00            4\n", "49850.00            4\n", "53650.00            4\n", "50600.00            4\n", "52075.00            4\n", "57775.00            4\n", "54625.00            4\n", "54200.00            4\n", "48575.00            4\n", "53075.00            4\n", "54350.00            4\n", "248000.00           4\n", "174000.00           4\n", "49825.00            4\n", "63400.00            4\n", "56075.00            4\n", "51575.00            4\n", "102000.00           4\n", "48675.00            4\n", "51425.00            4\n", "60250.00            4\n", "63550.00            4\n", "60375.00            4\n", "136000.00           4\n", "55150.00            4\n", "146000.00           4\n", "249000.00           4\n", "266000.00           4\n", "289000.00           4\n", "725.00              4\n", "950.00              4\n", "183000.00           4\n", "57175.00            4\n", "153000.00           4\n", "56700.00            4\n", "141000.00           4\n", "51225.00            4\n", "123000.00           4\n", "107000.00           4\n", "52900.00            4\n", "196000.00           4\n", "925.00              4\n", "51375.00            4\n", "49625.00            3\n", "58200.00            3\n", "54050.00            3\n", "51300.00            3\n", "55850.00            3\n", "53975.00            3\n", "62825.00            3\n", "51900.00            3\n", "50800.00            3\n", "50150.00            3\n", "58250.00            3\n", "63600.00            3\n", "64225.00            3\n", "56225.00            3\n", "218000.00           3\n", "247000.00           3\n", "278000.00           3\n", "207000.00           3\n", "46475.00            3\n", "55975.00            3\n", "60400.00            3\n", "375.00              3\n", "58725.00            3\n", "60925.00            3\n", "53925.00            3\n", "75100.00            3\n", "56450.00            3\n", "54225.00            3\n", "198000.00           3\n", "71600.00            3\n", "58100.00            3\n", "49475.00            3\n", "57075.00            3\n", "62400.00            3\n", "61600.00            3\n", "56675.00            3\n", "55100.00            3\n", "59200.00            3\n", "89500.00            3\n", "53875.00            3\n", "54700.00            3\n", "55250.00            3\n", "53800.00            3\n", "58225.00            3\n", "156000.00           3\n", "53450.00            3\n", "73600.00            3\n", "54275.00            3\n", "600000.00           3\n", "51275.00            3\n", "53425.00            3\n", "48250.00            3\n", "62475.00            3\n", "57600.00            3\n", "56750.00            3\n", "55700.00            3\n", "72500.00            3\n", "63950.00            3\n", "55050.00            3\n", "66575.00            3\n", "55200.00            3\n", "63350.00            3\n", "54775.00            3\n", "59125.00            3\n", "67600.00            3\n", "61625.00            3\n", "124000.00           3\n", "57700.00            3\n", "52875.00            3\n", "54925.00            3\n", "57150.00            3\n", "56725.00            3\n", "69500.00            3\n", "259000.00           3\n", "50625.00            3\n", "59625.00            3\n", "55775.00            3\n", "78700.00            3\n", "48775.00            3\n", "232000.00           3\n", "123500.00           3\n", "51400.00            3\n", "53500.00            3\n", "56050.00            3\n", "50375.00            3\n", "52175.00            3\n", "60475.00            3\n", "55750.00            3\n", "50850.00            3\n", "53825.00            3\n", "238000.00           3\n", "63100.00            3\n", "284000.00           3\n", "58700.00            3\n", "50925.00            3\n", "56600.00            3\n", "239000.00           3\n", "66775.00            3\n", "51950.00            3\n", "53025.00            3\n", "117000.00           3\n", "59500.00            3\n", "58750.00            3\n", "58175.00            3\n", "59700.00            3\n", "162000.00           3\n", "54975.00            3\n", "48750.00            3\n", "173000.00           3\n", "149900.00           3\n", "55725.00            3\n", "188000.00           3\n", "66175.00            3\n", "54825.00            3\n", "60100.00            3\n", "55375.00            3\n", "52475.00            3\n", "56200.00            3\n", "54175.00            3\n", "87700.00            3\n", "62875.00            3\n", "61500.00            3\n", "54900.00            3\n", "54025.00            3\n", "53350.00            3\n", "55875.00            3\n", "102500.00           2\n", "14999.00            2\n", "222200.00           2\n", "63075.00            2\n", "58900.00            2\n", "64675.00            2\n", "58525.00            2\n", "58625.00            2\n", "61275.00            2\n", "68950.00            2\n", "72600.00            2\n", "52600.00            2\n", "500000.00           2\n", "212000.00           2\n", "11812.00            2\n", "114800.00           2\n", "148500.00           2\n", "60200.00            2\n", "61025.00            2\n", "212500.00           2\n", "66700.00            2\n", "875.00              2\n", "59975.00            2\n", "59900.00            2\n", "92500.00            2\n", "83500.00            2\n", "52725.00            2\n", "68325.00            2\n", "69900.00            2\n", "450.00              2\n", "61525.00            2\n", "58375.00            2\n", "268000.00           2\n", "12763.00            2\n", "65625.00            2\n", "113700.00           2\n", "56100.00            2\n", "25.00               2\n", "56900.00            2\n", "66500.00            2\n", "62125.00            2\n", "56775.00            2\n", "56025.00            2\n", "68300.00            2\n", "56650.00            2\n", "68175.00            2\n", "1400000.00          2\n", "192500.00           2\n", "219000.00           2\n", "53125.00            2\n", "62075.00            2\n", "65725.00            2\n", "62950.00            2\n", "56850.00            2\n", "56325.00            2\n", "68675.00            2\n", "66250.00            2\n", "55575.00            2\n", "55125.00            2\n", "121000.00           2\n", "61950.00            2\n", "142000.00           2\n", "100900.00           2\n", "58125.00            2\n", "59250.00            2\n", "62100.00            2\n", "57800.00            2\n", "52100.00            2\n", "77500.00            2\n", "75325.00            2\n", "61650.00            2\n", "54375.00            2\n", "74400.00            2\n", "217000.00           2\n", "400.00              2\n", "57425.00            2\n", "61200.00            2\n", "60950.00            2\n", "63650.00            2\n", "56575.00            2\n", "141700.00           2\n", "186000.00           2\n", "67400.00            2\n", "257000.00           2\n", "171000.00           2\n", "57575.00            2\n", "63500.00            2\n", "64625.00            2\n", "62200.00            2\n", "56550.00            2\n", "99800.00            2\n", "58650.00            2\n", "60850.00            2\n", "51600.00            2\n", "57525.00            2\n", "80500.00            2\n", "263000.00           2\n", "51750.00            2\n", "52400.00            2\n", "211000.00           2\n", "163500.00           2\n", "58075.00            2\n", "63900.00            2\n", "50025.00            2\n", "133000.00           2\n", "71075.00            2\n", "51825.00            2\n", "54950.00            2\n", "66525.00            2\n", "60650.00            2\n", "54450.00            2\n", "76725.00            2\n", "82325.00            2\n", "76300.00            2\n", "60425.00            2\n", "89800.00            2\n", "55400.00            2\n", "58300.00            2\n", "14406.00            2\n", "97800.00            2\n", "62850.00            2\n", "179000.00           2\n", "63200.00            2\n", "53900.00            2\n", "55800.00            2\n", "51700.00            2\n", "57225.00            2\n", "55425.00            2\n", "8230.00             2\n", "56925.00            2\n", "56525.00            2\n", "425.00              2\n", "277000.00           2\n", "55525.00            2\n", "163000.00           2\n", "59650.00            2\n", "250100.00           2\n", "222000.00           2\n", "62275.00            2\n", "55600.00            2\n", "59475.00            2\n", "59050.00            2\n", "675.00              2\n", "54600.00            2\n", "62775.00            2\n", "69150.00            2\n", "350.00              2\n", "247500.00           2\n", "66425.00            2\n", "550.00              2\n", "66050.00            2\n", "73675.00            2\n", "189000.00           2\n", "65075.00            2\n", "79900.00            2\n", "88775.00            2\n", "66300.00            2\n", "60600.00            2\n", "85500.00            2\n", "250.00              2\n", "53225.00            2\n", "57975.00            2\n", "62300.00            2\n", "76900.00            2\n", "67200.00            2\n", "59025.00            2\n", "60050.00            2\n", "64450.00            2\n", "82500.00            2\n", "71900.00            2\n", "57100.00            2\n", "206000.00           2\n", "71950.00            2\n", "67900.00            2\n", "258000.00           2\n", "51550.00            2\n", "79500.00            2\n", "274000.00           2\n", "157000.00           2\n", "55650.00            2\n", "144400.00           2\n", "55925.00            2\n", "89900.00            2\n", "59300.00            2\n", "66950.00            2\n", "59325.00            2\n", "68725.00            2\n", "243000.00           2\n", "237000.00           2\n", "227000.00           2\n", "9220.00             2\n", "61725.00            2\n", "70400.00            2\n", "74800.00            2\n", "69600.00            2\n", "71500.00            2\n", "292000.00           2\n", "56300.00            2\n", "143000.00           2\n", "203000.00           2\n", "234000.00           2\n", "68500.00            2\n", "68625.00            2\n", "201000.00           2\n", "75600.00            2\n", "88900.00            2\n", "77400.00            2\n", "775.00              2\n", "55075.00            2\n", "66600.00            2\n", "65950.00            2\n", "66200.00            2\n", "62975.00            2\n", "16755.00            1\n", "92100.00            1\n", "10046.00            1\n", "54075.00            1\n", "74750.00            1\n", "119400.00           1\n", "27720.00            1\n", "7791.00             1\n", "26488.64            1\n", "61475.00            1\n", "4781.00             1\n", "184000.00           1\n", "196500.00           1\n", "157900.00           1\n", "227500.00           1\n", "67175.00            1\n", "80450.00            1\n", "12779.18            1\n", "67700.00            1\n", "234200.00           1\n", "64275.00            1\n", "6173.00             1\n", "285700.00           1\n", "12289.00            1\n", "65875.00            1\n", "65800.00            1\n", "123100.00           1\n", "31336.00            1\n", "60450.00            1\n", "4572.00             1\n", "26052.98            1\n", "19724.00            1\n", "18371.00            1\n", "58850.00            1\n", "85900.00            1\n", "73825.00            1\n", "283300.00           1\n", "192000.00           1\n", "19195.00            1\n", "94800.00            1\n", "255700.00           1\n", "39404.00            1\n", "23185.00            1\n", "68225.00            1\n", "67825.00            1\n", "92550.00            1\n", "108700.00           1\n", "66900.00            1\n", "25736.90            1\n", "63825.00            1\n", "283200.00           1\n", "59575.00            1\n", "14404.00            1\n", "55450.00            1\n", "87800.00            1\n", "58325.00            1\n", "78900.00            1\n", "65050.00            1\n", "286000.00           1\n", "4438.00             1\n", "249100.00           1\n", "95875.00            1\n", "57550.00            1\n", "74950.00            1\n", "57900.00            1\n", "63850.00            1\n", "58675.00            1\n", "25907.00            1\n", "77525.00            1\n", "254000.00           1\n", "69875.00            1\n", "71925.00            1\n", "153300.00           1\n", "89400.00            1\n", "133500.00           1\n", "154100.00           1\n", "208000.00           1\n", "90100.00            1\n", "59675.00            1\n", "85550.00            1\n", "75500.00            1\n", "59075.00            1\n", "15836.00            1\n", "26241.00            1\n", "59525.00            1\n", "225.00              1\n", "78050.00            1\n", "66350.00            1\n", "80050.00            1\n", "70975.00            1\n", "72975.00            1\n", "70450.00            1\n", "70150.00            1\n", "56825.00            1\n", "61850.00            1\n", "60625.00            1\n", "79025.00            1\n", "73125.00            1\n", "61425.00            1\n", "63300.00            1\n", "62700.00            1\n", "55325.00            1\n", "67575.00            1\n", "71250.00            1\n", "58925.00            1\n", "60875.00            1\n", "295400.00           1\n", "272000.00           1\n", "55475.00            1\n", "54675.00            1\n", "71375.00            1\n", "59450.00            1\n", "52250.00            1\n", "68825.00            1\n", "65325.00            1\n", "58575.00            1\n", "166650.00           1\n", "65250.00            1\n", "74200.00            1\n", "78350.00            1\n", "61875.00            1\n", "80525.00            1\n", "54725.00            1\n", "72450.00            1\n", "64150.00            1\n", "64375.00            1\n", "235300.00           1\n", "89850.00            1\n", "67350.00            1\n", "72300.00            1\n", "56425.00            1\n", "60125.00            1\n", "27286.00            1\n", "57275.00            1\n", "74300.00            1\n", "68900.00            1\n", "61100.00            1\n", "84275.00            1\n", "73250.00            1\n", "94950.00            1\n", "67800.00            1\n", "10360.00            1\n", "73875.00            1\n", "55950.00            1\n", "61825.00            1\n", "65750.00            1\n", "149600.00           1\n", "72875.00            1\n", "30383.00            1\n", "93400.00            1\n", "79775.00            1\n", "118425.00           1\n", "56150.00            1\n", "98600.00            1\n", "259100.00           1\n", "56875.00            1\n", "80900.00            1\n", "77900.00            1\n", "87075.00            1\n", "61050.00            1\n", "76800.00            1\n", "275300.00           1\n", "61150.00            1\n", "88450.00            1\n", "160800.00           1\n", "73550.00            1\n", "64950.00            1\n", "159100.00           1\n", "124800.00           1\n", "56375.00            1\n", "63050.00            1\n", "63800.00            1\n", "53179.00            1\n", "65650.00            1\n", "60775.00            1\n", "78800.00            1\n", "59425.00            1\n", "68150.00            1\n", "23924.00            1\n", "82425.00            1\n", "196300.00           1\n", "78300.00            1\n", "54850.00            1\n", "195800.00           1\n", "62750.00            1\n", "69325.00            1\n", "80025.00            1\n", "61125.00            1\n", "75625.00            1\n", "52350.00            1\n", "67625.00            1\n", "18227.00            1\n", "69050.00            1\n", "72700.00            1\n", "57875.00            1\n", "95500.00            1\n", "65475.00            1\n", "54800.00            1\n", "58150.00            1\n", "66550.00            1\n", "242000.00           1\n", "197000.00           1\n", "131000.00           1\n", "75.00               1\n", "222400.00           1\n", "72150.00            1\n", "193600.00           1\n", "286200.00           1\n", "63700.00            1\n", "162700.00           1\n", "65100.00            1\n", "17975.81            1\n", "117500.00           1\n", "69775.00            1\n", "104900.00           1\n", "96100.00            1\n", "84900.00            1\n", "73300.00            1\n", "94100.00            1\n", "122500.00           1\n", "137400.00           1\n", "246000.00           1\n", "274700.00           1\n", "56125.00            1\n", "229000.00           1\n", "64200.00            1\n", "77725.00            1\n", "296000.00           1\n", "93150.00            1\n", "220200.00           1\n", "60225.00            1\n", "78450.00            1\n", "68200.00            1\n", "92700.00            1\n", "29470.00            1\n", "85850.00            1\n", "77750.00            1\n", "267000.00           1\n", "70850.00            1\n", "72775.00            1\n", "230100.00           1\n", "57300.00            1\n", "78525.00            1\n", "68125.00            1\n", "64475.00            1\n", "58275.00            1\n", "64425.00            1\n", "73850.00            1\n", "58425.00            1\n", "24358.00            1\n", "71475.00            1\n", "256900.00           1\n", "64650.00            1\n", "61700.00            1\n", "62550.00            1\n", "30792.00            1\n", "275900.00           1\n", "67225.00            1\n", "70675.00            1\n", "22013.00            1\n", "226000.00           1\n", "61675.00            1\n", "56625.00            1\n", "71450.00            1\n", "68925.00            1\n", "209000.00           1\n", "75025.00            1\n", "285100.00           1\n", "57725.00            1\n", "64725.00            1\n", "93900.00            1\n", "297000.00           1\n", "138300.00           1\n", "79375.00            1\n", "61400.00            1\n", "80800.00            1\n", "213200.00           1\n", "159900.00           1\n", "222300.00           1\n", "149500.00           1\n", "81300.00            1\n", "110800.00           1\n", "57400.00            1\n", "106800.00           1\n", "296500.00           1\n", "269000.00           1\n", "75200.00            1\n", "234600.00           1\n", "294800.00           1\n", "55275.00            1\n", "450000.00           1\n", "66075.00            1\n", "124500.00           1\n", "182000.00           1\n", "283800.00           1\n", "92600.00            1\n", "110400.00           1\n", "121300.00           1\n", "9219.00             1\n", "57450.00            1\n", "91575.00            1\n", "380000.00           1\n", "64800.00            1\n", "66800.00            1\n", "287000.00           1\n", "64900.00            1\n", "18213.00            1\n", "21138.00            1\n", "103800.00           1\n", "19555.00            1\n", "7732.28             1\n", "179100.00           1\n", "188500.00           1\n", "33566.00            1\n", "227100.00           1\n", "78600.00            1\n", "175400.00           1\n", "93500.00            1\n", "88400.00            1\n", "87750.00            1\n", "239900.00           1\n", "122200.00           1\n", "131500.00           1\n", "236000.00           1\n", "113600.00           1\n", "166700.00           1\n", "82400.00            1\n", "70350.00            1\n", "86400.00            1\n", "76100.00            1\n", "151000.00           1\n", "157500.00           1\n", "83525.00            1\n", "87600.00            1\n", "164100.00           1\n", "101300.00           1\n", "61450.00            1\n", "72350.00            1\n", "68450.00            1\n", "200900.00           1\n", "60550.00            1\n", "111200.00           1\n", "67775.00            1\n", "62150.00            1\n", "59350.00            1\n", "57925.00            1\n", "83200.00            1\n", "81175.00            1\n", "56350.00            1\n", "81425.00            1\n", "70900.00            1\n", "63675.00            1\n", "210200.00           1\n", "58400.00            1\n", "216000.00           1\n", "59275.00            1\n", "245100.00           1\n", "89700.00            1\n", "10391.00            1\n", "31652.00            1\n", "291000.00           1\n", "91700.00            1\n", "50.00               1\n", "61575.00            1\n", "20075.56            1\n", "78125.00            1\n", "26175.71            1\n", "63150.00            1\n", "91125.00            1\n", "66375.00            1\n", "71425.00            1\n", "57350.00            1\n", "67250.00            1\n", "63250.00            1\n", "19250.56            1\n", "251000.00           1\n", "89100.00            1\n", "67075.00            1\n", "224400.00           1\n", "128500.00           1\n", "155500.00           1\n", "53775.00            1\n", "17350.30            1\n", "193500.00           1\n", "84500.00            1\n", "150.00              1\n", "62675.00            1\n", "72650.00            1\n", "69250.00            1\n", "17748.83            1\n", "86800.00            1\n", "69450.00            1\n", "69575.00            1\n", "74500.00            1\n", "58025.00            1\n", "64100.00            1\n", "57825.00            1\n", "72100.00            1\n", "65425.00            1\n", "79050.00            1\n", "62225.00            1\n", "57475.00            1\n", "63775.00            1\n", "223700.00           1\n", "239800.00           1\n", "57625.00            1\n", "20765.00            1\n", "8737.00             1\n", "24446.00            1\n", "13028.00            1\n", "4735.00             1\n", "19718.00            1\n", "16565.00            1\n", "23220.00            1\n", "15418.85            1\n", "19380.00            1\n", "10698.00            1\n", "13086.00            1\n", "7689.00             1\n", "15984.00            1\n", "22627.00            1\n", "12065.00            1\n", "7711.00             1\n", "14590.00            1\n", "267500.00           1\n", "13592.00            1\n", "16927.00            1\n", "22380.00            1\n", "18476.00            1\n", "12929.98            1\n", "15373.78            1\n", "9143.00             1\n", "18710.00            1\n", "20818.21            1\n", "15336.00            1\n", "10326.00            1\n", "18042.00            1\n", "23493.00            1\n", "23011.00            1\n", "18737.00            1\n", "13427.00            1\n", "14807.00            1\n", "17988.00            1\n", "20463.00            1\n", "18918.25            1\n", "23053.00            1\n", "6380.00             1\n", "3655.28             1\n", "22479.00            1\n", "15760.00            1\n", "17686.00            1\n", "6720.91             1\n", "5730.00             1\n", "10801.28            1\n", "15405.00            1\n", "7945.00             1\n", "19595.00            1\n", "9820.00             1\n", "15655.00            1\n", "7595.00             1\n", "5560.00             1\n", "14092.23            1\n", "23545.68            1\n", "19381.65            1\n", "16605.00            1\n", "8951.00             1\n", "12333.68            1\n", "8651.00             1\n", "20407.56            1\n", "15809.00            1\n", "4999.00             1\n", "18424.99            1\n", "15091.00            1\n", "18985.00            1\n", "22262.00            1\n", "6999.00             1\n", "13635.00            1\n", "21529.00            1\n", "16772.00            1\n", "24868.00            1\n", "19565.00            1\n", "12492.95            1\n", "8480.00             1\n", "15677.00            1\n", "9776.00             1\n", "11822.00            1\n", "71675.00            1\n", "15210.00            1\n", "4407.00             1\n", "12950.93            1\n", "10576.48            1\n", "15569.00            1\n", "12113.00            1\n", "16817.48            1\n", "9899.00             1\n", "19233.00            1\n", "14676.65            1\n", "19407.78            1\n", "15612.00            1\n", "9599.00             1\n", "21820.00            1\n", "12995.00            1\n", "3599.00             1\n", "11348.00            1\n", "14310.00            1\n", "22039.00            1\n", "17920.00            1\n", "16222.00            1\n", "11205.00            1\n", "18041.00            1\n", "21802.00            1\n", "18823.09            1\n", "22677.40            1\n", "24393.00            1\n", "17357.00            1\n", "21602.00            1\n", "22967.73            1\n", "10206.00            1\n", "12690.00            1\n", "10999.00            1\n", "7604.00             1\n", "10877.00            1\n", "17316.00            1\n", "16281.85            1\n", "11492.00            1\n", "15574.00            1\n", "17560.00            1\n", "22056.00            1\n", "9644.00             1\n", "10042.00            1\n", "22167.81            1\n", "18740.00            1\n", "7839.96             1\n", "14059.00            1\n", "15538.58            1\n", "15335.00            1\n", "19994.00            1\n", "14497.00            1\n", "24387.00            1\n", "23770.00            1\n", "17863.00            1\n", "19461.00            1\n", "17489.00            1\n", "17605.00            1\n", "9464.95             1\n", "6889.83             1\n", "19593.00            1\n", "15887.00            1\n", "22279.00            1\n", "11496.87            1\n", "19270.97            1\n", "24536.00            1\n", "20715.00            1\n", "10101.00            1\n", "61775.00            1\n", "16271.00            1\n", "9670.00             1\n", "23413.00            1\n", "10930.00            1\n", "4715.00             1\n", "22186.00            1\n", "20110.00            1\n", "2695.00             1\n", "12298.50            1\n", "19118.87            1\n", "28355.17            1\n", "14461.24            1\n", "11742.49            1\n", "14915.00            1\n", "23354.13            1\n", "12197.00            1\n", "3989.00             1\n", "23230.00            1\n", "16105.00            1\n", "3430.00             1\n", "27248.00            1\n", "20595.00            1\n", "25620.70            1\n", "6329.00             1\n", "19742.00            1\n", "11219.60            1\n", "21600.89            1\n", "12480.00            1\n", "21558.51            1\n", "13392.00            1\n", "6426.00             1\n", "29418.18            1\n", "1926.00             1\n", "23720.42            1\n", "16413.00            1\n", "15419.30            1\n", "12580.00            1\n", "10585.00            1\n", "23738.00            1\n", "12538.00            1\n", "28710.00            1\n", "15600.88            1\n", "11796.00            1\n", "21051.00            1\n", "22981.00            1\n", "1564.00             1\n", "27347.00            1\n", "28252.00            1\n", "5495.00             1\n", "28584.13            1\n", "12078.00            1\n", "3288.00             1\n", "14135.00            1\n", "14862.00            1\n", "14339.00            1\n", "9995.00             1\n", "21610.00            1\n", "13312.00            1\n", "12525.13            1\n", "14432.00            1\n", "14071.00            1\n", "14699.00            1\n", "9458.00             1\n", "22222.00            1\n", "24032.65            1\n", "21972.00            1\n", "19174.00            1\n", "3980.00             1\n", "14085.00            1\n", "7592.00             1\n", "18238.00            1\n", "26381.00            1\n", "23937.00            1\n", "4359.00             1\n", "15368.00            1\n", "23408.00            1\n", "14152.00            1\n", "19720.00            1\n", "11497.97            1\n", "5153.65             1\n", "28686.49            1\n", "16948.00            1\n", "25378.31            1\n", "5891.00             1\n", "21601.00            1\n", "17123.00            1\n", "4552.14             1\n", "29184.20            1\n", "8940.00             1\n", "12796.20            1\n", "28344.00            1\n", "8820.00             1\n", "17053.00            1\n", "20876.00            1\n", "9930.00             1\n", "9755.00             1\n", "1560.00             1\n", "14696.00            1\n", "12001.00            1\n", "5598.00             1\n", "9098.83             1\n", "24424.00            1\n", "14739.00            1\n", "22555.00            1\n", "15678.00            1\n", "11142.00            1\n", "13068.00            1\n", "28565.00            1\n", "13830.00            1\n", "21748.30            1\n", "17671.00            1\n", "14824.00            1\n", "10767.00            1\n", "15757.00            1\n", "1828.50             1\n", "16655.00            1\n", "20129.00            1\n", "16304.00            1\n", "17309.00            1\n", "7685.00             1\n", "11080.00            1\n", "12460.00            1\n", "7355.00             1\n", "13877.00            1\n", "9490.00             1\n", "11364.00            1\n", "8579.95             1\n", "7005.82             1\n", "12779.00            1\n", "17979.00            1\n", "5680.00             1\n", "16609.00            1\n", "3420.00             1\n", "11827.00            1\n", "7228.12             1\n", "7573.92             1\n", "28524.54            1\n", "29572.00            1\n", "17460.59            1\n", "14159.23            1\n", "19227.00            1\n", "23928.74            1\n", "11103.00            1\n", "22841.00            1\n", "21735.00            1\n", "22868.68            1\n", "15609.00            1\n", "11273.00            1\n", "71625.00            1\n", "125.00              1\n", "53300.00            1\n", "100.00              1\n", "53275.00            1\n", "71100.00            1\n", "294000.00           1\n", "70225.00            1\n", "175.00              1\n", "73775.00            1\n", "70250.00            1\n", "52700.00            1\n", "91100.00            1\n", "64975.00            1\n", "74875.00            1\n", "172500.00           1\n", "60525.00            1\n", "62425.00            1\n", "75700.00            1\n", "58450.00            1\n", "68850.00            1\n", "57375.00            1\n", "74175.00            1\n", "66475.00            1\n", "66825.00            1\n", "65825.00            1\n", "76925.00            1\n", "62350.00            1\n", "67950.00            1\n", "76125.00            1\n", "56275.00            1\n", "88425.00            1\n", "75550.00            1\n", "85075.00            1\n", "67725.00            1\n", "67275.00            1\n", "69125.00            1\n", "80400.00            1\n", "257600.00           1\n", "76200.00            1\n", "89350.00            1\n", "84650.00            1\n", "60675.00            1\n", "68775.00            1\n", "63450.00            1\n", "19999.00            1\n", "133300.00           1\n", "130725.00           1\n", "61800.00            1\n", "55825.00            1\n", "244000.00           1\n", "234300.00           1\n", "72850.00            1\n", "69725.00            1\n", "194500.00           1\n", "94275.00            1\n", "69425.00            1\n", "58550.00            1\n", "70875.00            1\n", "62375.00            1\n", "86125.00            1\n", "65025.00            1\n", "136500.00           1\n", "69675.00            1\n", "70200.00            1\n", "292300.00           1\n", "74375.00            1\n", "69700.00            1\n", "295100.00           1\n", "63325.00            1\n", "53700.00            1\n", "73800.00            1\n", "59825.00            1\n", "64175.00            1\n", "85800.00            1\n", "74700.00            1\n", "213600.00           1\n", "283000.00           1\n", "57200.00            1\n", "59750.00            1\n", "58875.00            1\n", "60500.00            1\n", "69800.00            1\n", "71025.00            1\n", "228000.00           1\n", "325000.00           1\n", "177000.00           1\n", "98700.00            1\n", "112500.00           1\n", "146500.00           1\n", "150100.00           1\n", "90725.00            1\n", "266500.00           1\n", "21203.36            1\n", "70375.00            1\n", "276900.00           1\n", "29125.34            1\n", "19400.64            1\n", "84975.00            1\n", "56975.00            1\n", "62025.00            1\n", "71350.00            1\n", "58975.00            1\n", "71750.00            1\n", "81500.00            1\n", "200500.00           1\n", "297700.00           1\n", "288700.00           1\n", "279000.00           1\n", "85100.00            1\n", "176000.00           1\n", "76500.00            1\n", "84450.00            1\n", "78825.00            1\n", "241000.00           1\n", "70500.00            1\n", "96525.00            1\n", "64575.00            1\n", "78150.00            1\n", "73175.00            1\n", "62600.00            1\n", "102325.00           1\n", "231000.00           1\n", "85300.00            1\n", "91850.00            1\n", "63425.00            1\n", "68425.00            1\n", "69025.00            1\n", "66150.00            1\n", "57850.00            1\n", "73100.00            1\n", "84800.00            1\n", "75800.00            1\n", "75900.00            1\n", "73200.00            1\n", "62800.00            1\n", "96500.00            1\n", "77600.00            1\n", "61225.00            1\n", "75400.00            1\n", "94900.00            1\n", "49999.00            1\n", "96950.00            1\n", "80625.00            1\n", "17674.00            1\n", "67675.00            1\n", "84125.00            1\n", "20801.47            1\n", "16914.36            1\n", "20377.40            1\n", "13921.00            1\n", "12598.00            1\n", "10540.00            1\n", "9191.00             1\n", "17340.00            1\n", "1512.00             1\n", "11999.00            1\n", "12017.00            1\n", "18372.00            1\n", "18852.00            1\n", "19424.95            1\n", "19808.64            1\n", "13143.00            1\n", "17066.62            1\n", "17702.00            1\n", "14980.00            1\n", "12760.68            1\n", "8313.00             1\n", "17554.00            1\n", "13133.00            1\n", "4251.28             1\n", "20116.00            1\n", "16123.00            1\n", "9083.00             1\n", "19974.00            1\n", "15718.00            1\n", "18088.68            1\n", "14161.00            1\n", "18420.00            1\n", "18334.00            1\n", "10394.00            1\n", "10021.53            1\n", "7753.08             1\n", "9692.00             1\n", "11371.85            1\n", "7174.00             1\n", "15998.00            1\n", "21754.00            1\n", "19701.00            1\n", "11882.00            1\n", "13079.00            1\n", "14989.00            1\n", "14228.00            1\n", "9555.50             1\n", "21365.00            1\n", "15015.00            1\n", "18948.00            1\n", "15354.00            1\n", "16167.00            1\n", "14263.00            1\n", "15424.22            1\n", "15045.25            1\n", "24716.00            1\n", "14747.00            1\n", "19865.00            1\n", "24279.38            1\n", "16995.00            1\n", "17060.00            1\n", "11542.00            1\n", "12419.00            1\n", "15329.00            1\n", "19612.63            1\n", "19994.86            1\n", "10866.00            1\n", "11272.00            1\n", "20390.00            1\n", "23721.00            1\n", "16696.00            1\n", "3152.00             1\n", "18533.00            1\n", "1849.00             1\n", "61175.00            1\n", "18176.20            1\n", "120500.00           1\n", "6180.00             1\n", "10830.00            1\n", "22650.58            1\n", "17814.00            1\n", "17480.00            1\n", "13604.00            1\n", "11135.00            1\n", "8489.00             1\n", "19084.00            1\n", "11693.34            1\n", "8381.00             1\n", "1200000.00          1\n", "1000000.00          1\n", "9544.55             1\n", "287500.00           1\n", "223000.00           1\n", "215500.00           1\n", "86675.00            1\n", "70950.00            1\n", "1685.00             1\n", "75675.00            1\n", "81075.00            1\n", "175500.00           1\n", "67525.00            1\n", "63725.00            1\n", "120900.00           1\n", "66225.00            1\n", "202200.00           1\n", "235800.00           1\n", "82700.00            1\n", "76600.00            1\n", "202000.00           1\n", "16240.00            1\n", "13945.00            1\n", "8420.00             1\n", "17493.00            1\n", "7346.00             1\n", "17337.00            1\n", "22543.00            1\n", "625.00              1\n", "19510.00            1\n", "21992.00            1\n", "3680.00             1\n", "4399.00             1\n", "6788.00             1\n", "11378.99            1\n", "8091.77             1\n", "4820.72             1\n", "10695.00            1\n", "4495.00             1\n", "6793.00             1\n", "11960.00            1\n", "10303.00            1\n", "15387.00            1\n", "21098.00            1\n", "21095.00            1\n", "11732.00            1\n", "9823.00             1\n", "15183.00            1\n", "5048.00             1\n", "23195.00            1\n", "8106.00             1\n", "24120.00            1\n", "18480.00            1\n", "11894.96            1\n", "4787.00             1\n", "5099.34             1\n", "22484.00            1\n", "18570.00            1\n", "64075.00            1\n", "Name: count, dtype: int64\n", "==============================\n", "Application Date\n", "['2007-05-26' '2007-05-27' '2007-05-28' ... '2016-12-29' '2016-12-30'\n", " '2016-12-31']\n", "Application Date\n", "2018-12-04    42112\n", "2018-09-12    40849\n", "2018-12-05    40394\n", "2018-05-14    40390\n", "2018-12-10    40055\n", "2018-08-27    39213\n", "2018-05-15    39170\n", "2018-08-15    39155\n", "2018-12-11    39132\n", "2018-10-10    38964\n", "2018-12-03    38887\n", "2018-11-27    38836\n", "2018-09-10    38706\n", "2018-10-03    38670\n", "2018-10-22    38663\n", "2018-09-18    38315\n", "2018-07-18    38278\n", "2018-07-16    38109\n", "2018-07-17    37994\n", "2018-07-25    37948\n", "2018-11-14    37925\n", "2018-07-24    37545\n", "2018-10-01    37500\n", "2018-08-14    37382\n", "2018-10-17    37381\n", "2018-11-29    37344\n", "2018-11-07    37293\n", "2018-09-05    37259\n", "2018-08-29    37077\n", "2018-12-12    36940\n", "2018-05-17    36706\n", "2018-05-02    36641\n", "2018-10-02    36619\n", "2018-05-29    36595\n", "2018-05-30    36541\n", "2018-08-20    36289\n", "2018-08-21    36189\n", "2018-05-16    36179\n", "2018-11-26    35910\n", "2018-10-24    35884\n", "2018-10-15    35830\n", "2018-11-13    35750\n", "2018-08-22    35724\n", "2018-04-25    35709\n", "2018-05-09    35682\n", "2018-07-30    35622\n", "2018-10-16    35573\n", "2018-08-16    35513\n", "2018-12-06    35441\n", "2018-08-28    35438\n", "2018-05-22    35435\n", "2018-06-13    35409\n", "2018-05-07    35321\n", "2018-12-17    35270\n", "2018-06-06    35258\n", "2018-09-04    35203\n", "2018-08-08    34997\n", "2018-05-01    34973\n", "2018-04-23    34921\n", "2018-04-30    34888\n", "2018-05-08    34871\n", "2018-11-05    34816\n", "2018-07-31    34662\n", "2018-10-23    34409\n", "2018-06-05    34408\n", "2018-08-13    34382\n", "2018-09-19    34351\n", "2018-11-06    34319\n", "2018-05-23    34274\n", "2018-10-29    34228\n", "2018-09-11    34215\n", "2018-09-06    34041\n", "2018-11-12    34036\n", "2018-11-28    34024\n", "2018-11-20    34007\n", "2018-05-21    33937\n", "2018-12-19    33898\n", "2018-12-27    33737\n", "2018-10-11    33734\n", "2018-06-19    33640\n", "2018-04-16    33635\n", "2018-07-12    33554\n", "2017-07-19    33476\n", "2018-06-12    33370\n", "2018-10-09    33345\n", "2018-04-24    33326\n", "2018-10-08    33293\n", "2018-05-10    33236\n", "2018-07-19    33183\n", "2018-07-11    33161\n", "2018-08-07    33136\n", "2018-12-18    33126\n", "2018-09-17    33053\n", "2018-06-18    33033\n", "2018-05-03    33016\n", "2017-11-15    33014\n", "2018-11-15    33012\n", "2018-10-18    32946\n", "2017-08-16    32917\n", "2018-07-09    32678\n", "2018-07-10    32635\n", "2017-12-11    32634\n", "2017-10-25    32627\n", "2017-07-17    32615\n", "2018-06-04    32610\n", "2017-05-31    32592\n", "2018-10-04    32516\n", "2017-11-14    32494\n", "2018-04-17    32491\n", "2018-04-18    32416\n", "2018-06-20    32412\n", "2018-11-08    32299\n", "2017-07-26    31973\n", "2017-09-20    31900\n", "2018-11-19    31827\n", "2017-11-08    31792\n", "2018-07-26    31711\n", "2018-10-25    31626\n", "2018-10-30    31594\n", "2017-11-09    31495\n", "2018-06-11    31495\n", "2018-08-23    31466\n", "2017-07-18    31395\n", "2018-08-02    31336\n", "2018-07-13    31224\n", "2017-11-13    31133\n", "2017-10-16    31057\n", "2018-12-13    31033\n", "2016-07-05    31010\n", "2018-09-20    30902\n", "2017-07-10    30900\n", "2018-06-14    30882\n", "2017-09-18    30823\n", "2017-11-29    30804\n", "2017-12-06    30760\n", "2017-10-18    30754\n", "2017-11-20    30678\n", "2018-09-25    30559\n", "2018-06-21    30549\n", "2017-09-25    30392\n", "2018-06-26    30354\n", "2018-08-01    30303\n", "2017-12-04    30272\n", "2018-11-01    30242\n", "2017-09-19    30227\n", "2018-05-24    30149\n", "2017-10-11    30099\n", "2018-06-25    30027\n", "2017-08-30    29821\n", "2018-03-05    29784\n", "2018-09-13    29777\n", "2017-12-05    29766\n", "2018-04-11    29757\n", "2017-10-23    29745\n", "2017-09-26    29692\n", "2017-10-24    29677\n", "2018-04-26    29634\n", "2016-11-14    29586\n", "2018-08-09    29554\n", "2017-11-27    29533\n", "2018-04-09    29442\n", "2017-11-28    29438\n", "2018-04-04    29383\n", "2018-09-24    29374\n", "2018-08-30    29346\n", "2018-07-23    29337\n", "2018-01-24    29321\n", "2018-01-03    29280\n", "2017-07-11    29269\n", "2016-11-16    29184\n", "2018-05-31    29152\n", "2017-08-28    29138\n", "2017-08-02    29066\n", "2018-01-17    29039\n", "2018-04-10    29017\n", "2017-11-16    28989\n", "2017-07-12    28932\n", "2018-10-31    28895\n", "2018-03-21    28847\n", "2018-05-18    28823\n", "2017-12-13    28800\n", "2017-11-07    28795\n", "2018-03-19    28772\n", "2018-05-11    28748\n", "2018-12-07    28722\n", "2018-04-19    28721\n", "2017-10-09    28719\n", "2017-12-18    28710\n", "2018-06-07    28704\n", "2017-12-12    28689\n", "2018-07-02    28547\n", "2017-07-31    28542\n", "2017-10-17    28451\n", "2017-09-13    28433\n", "2017-11-06    28340\n", "2017-07-25    28282\n", "2017-09-27    28270\n", "2017-09-06    28224\n", "2018-08-06    28215\n", "2018-01-10    28211\n", "2018-09-14    28206\n", "2018-03-26    28201\n", "2018-09-03    28178\n", "2018-09-26    28149\n", "2017-11-21    28122\n", "2018-06-27    28067\n", "2018-01-22    28065\n", "2017-11-02    28043\n", "2018-12-20    27899\n", "2017-07-24    27839\n", "2017-09-21    27825\n", "2017-10-04    27807\n", "2017-11-01    27788\n", "2018-08-17    27765\n", "2017-08-01    27680\n", "2017-09-05    27650\n", "2018-03-14    27633\n", "2017-06-05    27334\n", "2018-04-02    27303\n", "2018-06-15    27297\n", "2018-03-07    27162\n", "2016-06-15    27127\n", "2018-04-03    27124\n", "2018-09-07    27123\n", "2018-07-05    27068\n", "2018-11-30    27016\n", "2018-03-20    27012\n", "2018-03-12    26952\n", "2017-10-12    26877\n", "2017-11-30    26862\n", "2018-09-27    26822\n", "2017-12-19    26788\n", "2017-06-07    26773\n", "2017-05-22    26709\n", "2017-05-30    26673\n", "2017-05-17    26625\n", "2016-11-15    26612\n", "2017-12-20    26594\n", "2018-12-26    26561\n", "2017-07-27    26530\n", "2018-02-21    26502\n", "2018-12-28    26416\n", "2017-05-24    26356\n", "2017-09-11    26311\n", "2017-08-29    26283\n", "2018-11-16    26280\n", "2018-05-25    26275\n", "2017-08-15    26229\n", "2018-02-20    26217\n", "2017-07-20    26199\n", "2018-01-09    26190\n", "2018-03-06    26171\n", "2018-07-20    26130\n", "2017-09-14    26128\n", "2018-03-13    26105\n", "2018-06-22    26095\n", "2018-02-12    26005\n", "2017-10-30    26000\n", "2018-03-27    25970\n", "2017-08-23    25943\n", "2018-01-16    25922\n", "2017-10-26    25840\n", "2016-12-12    25749\n", "2018-06-01    25747\n", "2018-04-12    25723\n", "2017-10-02    25718\n", "2017-08-09    25691\n", "2018-03-28    25686\n", "2017-06-12    25648\n", "2017-06-14    25639\n", "2017-12-07    25632\n", "2018-08-03    25618\n", "2017-06-01    25580\n", "2018-11-09    25577\n", "2017-10-31    25573\n", "2017-12-27    25562\n", "2018-01-29    25531\n", "2017-09-12    25529\n", "2018-12-14    25511\n", "2018-01-23    25495\n", "2017-06-28    25458\n", "2018-07-03    25434\n", "2017-10-10    25415\n", "2017-07-13    25398\n", "2016-12-14    25375\n", "2016-07-06    25347\n", "2018-01-08    25312\n", "2018-10-12    25291\n", "2017-08-31    25285\n", "2018-02-13    25253\n", "2018-06-28    25250\n", "2018-08-24    25235\n", "2018-01-31    25235\n", "2017-09-07    25217\n", "2018-01-15    25202\n", "2018-10-26    25198\n", "2017-09-28    25190\n", "2018-10-19    25149\n", "2018-01-02    25109\n", "2018-04-05    25023\n", "2017-10-19    25014\n", "2018-01-04    25005\n", "2016-08-29    24998\n", "2017-05-23    24978\n", "2017-10-05    24913\n", "2018-01-25    24906\n", "2018-05-04    24900\n", "2018-02-28    24754\n", "2018-02-07    24742\n", "2016-12-05    24625\n", "2018-09-21    24522\n", "2018-11-02    24511\n", "2017-08-17    24433\n", "2017-06-06    24414\n", "2018-01-30    24408\n", "2016-08-09    24271\n", "2018-02-06    24264\n", "2018-10-05    24227\n", "2017-06-21    24180\n", "2018-04-27    24107\n", "2018-02-19    24107\n", "2018-08-10    24076\n", "2017-08-14    24072\n", "2018-07-06    24032\n", "2017-11-22    23999\n", "2018-01-18    23908\n", "2018-02-26    23906\n", "2018-07-27    23888\n", "2017-06-19    23879\n", "2018-04-20    23853\n", "2017-12-14    23820\n", "2018-01-11    23813\n", "2018-06-29    23786\n", "2018-03-22    23756\n", "2016-12-07    23620\n", "2017-10-03    23609\n", "2017-08-22    23562\n", "2018-11-21    23548\n", "2017-05-03    23542\n", "2016-11-21    23537\n", "2017-06-20    23532\n", "2018-03-15    23508\n", "2017-08-24    23488\n", "2018-06-08    23477\n", "2018-09-28    23451\n", "2016-11-28    23448\n", "2016-10-11    23429\n", "2017-07-06    23365\n", "2017-05-15    23339\n", "2018-03-08    23314\n", "2015-05-14    23298\n", "2017-06-27    23294\n", "2017-11-10    23251\n", "2018-02-22    23230\n", "2017-04-24    23178\n", "2016-08-24    23158\n", "2017-06-26    23149\n", "2018-08-31    23148\n", "2017-03-01    23075\n", "2017-08-10    23062\n", "2017-08-08    23025\n", "2017-04-26    22993\n", "2016-11-30    22950\n", "2018-12-08    22897\n", "2018-02-14    22888\n", "2018-08-18    22843\n", "2017-07-21    22737\n", "2017-03-29    22648\n", "2018-03-29    22603\n", "2017-06-15    22599\n", "2018-02-05    22593\n", "2017-05-08    22546\n", "2017-04-12    22544\n", "2017-06-13    22431\n", "2017-07-05    22388\n", "2018-02-27    22370\n", "2017-08-07    22348\n", "2017-05-01    22341\n", "2018-02-01    22332\n", "2017-11-17    22282\n", "2017-08-21    22179\n", "2016-12-06    22158\n", "2018-02-15    22152\n", "2016-06-23    22114\n", "2017-12-28    22105\n", "2017-09-04    22095\n", "2016-06-27    22085\n", "2016-12-13    22047\n", "2017-04-25    22011\n", "2017-05-25    21981\n", "2018-11-23    21947\n", "2017-04-04    21944\n", "2017-01-11    21849\n", "2017-01-04    21828\n", "2016-03-21    21821\n", "2017-05-18    21782\n", "2017-08-03    21777\n", "2016-11-29    21750\n", "2017-01-23    21695\n", "2017-05-02    21590\n", "2018-03-01    21585\n", "2016-09-12    21575\n", "2017-07-28    21533\n", "2017-06-29    21520\n", "2018-02-08    21516\n", "2018-05-28    21449\n", "2018-12-31    21440\n", "2017-04-19    21383\n", "2016-06-28    21372\n", "2018-08-19    21352\n", "2016-08-11    21338\n", "2018-12-21    21296\n", "2017-09-22    21268\n", "2017-12-21    21260\n", "2017-02-13    21258\n", "2017-06-08    21230\n", "2016-11-17    21199\n", "2018-11-25    21097\n", "2017-05-10    21088\n", "2018-12-02    21056\n", "2016-08-08    21052\n", "2016-06-21    21029\n", "2017-01-09    20976\n", "2018-09-08    20946\n", "2017-07-07    20936\n", "2016-03-22    20928\n", "2016-09-06    20823\n", "2017-06-22    20772\n", "2018-11-11    20758\n", "2017-09-15    20723\n", "2017-01-25    20721\n", "2016-10-17    20707\n", "2016-12-28    20698\n", "2017-03-27    20671\n", "2016-11-22    20648\n", "2018-04-13    20599\n", "2017-08-18    20596\n", "2017-04-05    20592\n", "2017-12-26    20583\n", "2017-03-06    20577\n", "2016-06-29    20521\n", "2018-10-21    20517\n", "2018-12-09    20514\n", "2018-09-09    20510\n", "2018-09-29    20505\n", "2018-10-27    20486\n", "2017-01-30    20486\n", "2018-11-17    20483\n", "2017-10-20    20457\n", "2017-12-08    20448\n", "2017-02-06    20417\n", "2016-12-19    20399\n", "2017-06-02    20389\n", "2017-01-18    20366\n", "2018-12-01    20297\n", "2016-08-17    20292\n", "2017-03-28    20267\n", "2017-01-10    20264\n", "2017-07-14    20261\n", "2016-03-09    20177\n", "2017-01-24    20158\n", "2016-06-22    20154\n", "2016-04-12    20145\n", "2018-06-24    20124\n", "2016-08-16    20087\n", "2018-10-20    20082\n", "2017-02-08    20039\n", "2017-04-03    20021\n", "2017-03-07    20011\n", "2016-10-19    20008\n", "2018-09-30    20008\n", "2016-12-21    20002\n", "2017-10-13    19981\n", "2016-04-11    19978\n", "2017-12-01    19969\n", "2017-05-09    19932\n", "2018-11-10    19923\n", "2016-09-01    19920\n", "2018-07-22    19886\n", "2018-09-15    19883\n", "2017-11-03    19867\n", "2018-11-18    19858\n", "2017-05-16    19834\n", "2016-11-02    19808\n", "2018-06-23    19795\n", "2016-10-12    19744\n", "2018-11-03    19723\n", "2016-08-15    19713\n", "2018-01-05    19671\n", "2018-07-21    19651\n", "2016-10-05    19615\n", "2017-04-18    19585\n", "2017-07-03    19568\n", "2016-10-18    19531\n", "2016-06-30    19514\n", "2018-08-04    19496\n", "2018-12-16    19473\n", "2017-01-19    19450\n", "2018-07-15    19450\n", "2017-02-27    19449\n", "2017-02-15    19448\n", "2016-03-30    19415\n", "2016-10-25    19405\n", "2018-08-26    19393\n", "2018-08-25    19369\n", "2018-11-04    19310\n", "2018-11-24    19307\n", "2018-10-13    19304\n", "2017-04-11    19284\n", "2017-02-21    19262\n", "2016-09-08    19253\n", "2018-06-10    19195\n", "2017-08-11    19184\n", "2017-12-15    19165\n", "2016-10-03    19163\n", "2017-10-27    19134\n", "2016-10-13    19128\n", "2018-07-14    19116\n", "2016-12-01    19115\n", "2016-03-14    19112\n", "2016-10-24    19101\n", "2018-01-19    19084\n", "2016-10-26    19078\n", "2016-09-26    19049\n", "2017-01-31    18976\n", "2017-08-04    18961\n", "2017-01-12    18937\n", "2018-08-12    18936\n", "2016-04-26    18934\n", "2018-03-16    18928\n", "2016-09-21    18925\n", "2016-11-01    18912\n", "2018-10-14    18903\n", "2018-07-28    18902\n", "2018-09-22    18889\n", "2018-12-15    18854\n", "2018-09-16    18846\n", "2017-05-04    18840\n", "2018-06-16    18829\n", "2017-01-17    18822\n", "2017-03-14    18815\n", "2018-10-06    18799\n", "2018-04-06    18792\n", "2016-11-07    18760\n", "2017-06-23    18759\n", "2016-03-08    18718\n", "2018-09-23    18711\n", "2017-02-01    18706\n", "2017-02-22    18693\n", "2016-12-08    18691\n", "2017-01-03    18691\n", "2018-03-09    18676\n", "2017-01-06    18656\n", "2018-08-11    18655\n", "2016-09-14    18652\n", "2017-03-20    18611\n", "2017-10-06    18595\n", "2018-12-29    18563\n", "2015-11-17    18550\n", "2018-01-12    18536\n", "2017-04-27    18504\n", "2018-06-09    18489\n", "2018-10-28    18488\n", "2016-03-16    18466\n", "2018-10-07    18443\n", "2018-09-01    18422\n", "2016-04-19    18393\n", "2015-10-15    18392\n", "2018-07-07    18389\n", "2017-09-08    18381\n", "2018-07-08    18364\n", "2018-03-23    18358\n", "2016-08-18    18347\n", "2016-12-20    18325\n", "2017-05-11    18223\n", "2018-08-05    18211\n", "2017-01-26    18211\n", "2018-06-03    18173\n", "2017-01-05    18147\n", "2018-07-01    18137\n", "2016-06-20    18125\n", "2017-06-30    18077\n", "2017-05-19    18008\n", "2017-02-20    17985\n", "2018-07-29    17948\n", "2017-09-01    17938\n", "2016-10-27    17935\n", "2015-07-13    17931\n", "2016-09-27    17910\n", "2017-03-30    17905\n", "2017-01-16    17904\n", "2016-03-28    17900\n", "2017-08-25    17887\n", "2017-03-08    17848\n", "2018-06-30    17844\n", "2016-10-31    17795\n", "2017-09-29    17789\n", "2018-05-12    17775\n", "2017-03-13    17772\n", "2018-05-19    17772\n", "2017-06-16    17768\n", "2018-12-30    17767\n", "2018-06-02    17745\n", "2017-04-06    17720\n", "2016-09-20    17689\n", "2016-05-03    17687\n", "2017-03-02    17626\n", "2018-01-26    17590\n", "2016-03-17    17589\n", "2018-03-30    17586\n", "2017-03-23    17547\n", "2018-05-20    17508\n", "2016-09-13    17506\n", "2016-07-07    17476\n", "2017-11-24    17465\n", "2018-06-17    17350\n", "2017-03-21    17344\n", "2017-12-29    17315\n", "2018-04-22    17281\n", "2017-02-07    17268\n", "2016-12-15    17252\n", "2017-03-22    17251\n", "2016-08-30    17243\n", "2016-04-05    17229\n", "2018-07-04    17207\n", "2017-05-26    17205\n", "2017-03-15    17184\n", "2016-05-09    17180\n", "2016-05-02    17158\n", "2016-12-27    17132\n", "2018-05-06    17126\n", "2018-04-21    17117\n", "2016-04-25    17106\n", "2016-09-07    17078\n", "2017-02-02    17068\n", "2017-02-28    17065\n", "2018-02-23    17036\n", "2018-02-09    17019\n", "2018-02-16    16983\n", "2016-03-23    16972\n", "2017-04-20    16878\n", "2016-09-28    16872\n", "2017-06-09    16872\n", "2018-09-02    16868\n", "2018-02-02    16799\n", "2016-09-19    16791\n", "2017-04-13    16774\n", "2018-03-02    16773\n", "2016-01-26    16753\n", "2018-05-05    16672\n", "2018-12-24    16602\n", "2016-07-26    16584\n", "2018-05-13    16498\n", "2015-12-15    16418\n", "2017-05-05    16386\n", "2015-07-01    16329\n", "2016-03-15    16307\n", "2016-06-10    16304\n", "2015-07-06    16300\n", "2017-04-10    16277\n", "2016-11-09    16269\n", "2018-04-15    16266\n", "2017-04-21    16245\n", "2016-02-09    16243\n", "2016-11-03    16166\n", "2016-12-29    16162\n", "2017-04-17    16049\n", "2018-04-28    16007\n", "2017-03-16    16007\n", "2016-10-20    15969\n", "2016-03-24    15936\n", "2016-08-10    15929\n", "2016-03-07    15917\n", "2017-02-16    15903\n", "2017-02-14    15896\n", "2018-04-29    15893\n", "2017-03-03    15855\n", "2016-03-10    15835\n", "2017-02-23    15811\n", "2016-11-10    15809\n", "2016-11-08    15786\n", "2016-07-11    15739\n", "2018-04-14    15717\n", "2016-07-27    15702\n", "2018-05-26    15689\n", "2016-10-10    15637\n", "2018-12-22    15632\n", "2016-04-13    15569\n", "2017-11-18    15568\n", "2017-04-28    15521\n", "2016-10-04    15512\n", "2016-07-25    15508\n", "2015-12-29    15494\n", "2017-03-09    15477\n", "2016-12-30    15457\n", "2016-08-25    15379\n", "2015-06-26    15307\n", "2017-07-08    15291\n", "2017-02-09    15270\n", "2017-07-15    15170\n", "2017-07-22    15136\n", "2018-01-13    15131\n", "2017-07-29    15118\n", "2015-12-08    15107\n", "2017-12-22    15103\n", "2018-04-08    15053\n", "2016-07-19    15046\n", "2015-12-28    15034\n", "2016-03-18    15033\n", "2016-04-18    15026\n", "2016-07-12    14999\n", "2015-12-16    14980\n", "2018-04-07    14974\n", "2018-05-27    14918\n", "2016-12-02    14899\n", "2016-11-18    14895\n", "2016-07-08    14807\n", "2016-04-27    14751\n", "2016-03-29    14735\n", "2015-01-07    14715\n", "2016-07-20    14710\n", "2017-09-16    14680\n", "2017-11-26    14655\n", "2017-06-04    14622\n", "2015-11-24    14596\n", "2016-04-07    14585\n", "2017-07-09    14584\n", "2015-09-22    14570\n", "2016-08-31    14562\n", "2017-06-03    14556\n", "2016-01-12    14555\n", "2016-02-16    14552\n", "2017-12-02    14550\n", "2014-08-05    14540\n", "2016-05-04    14515\n", "2016-04-14    14500\n", "2016-02-18    14485\n", "2017-12-09    14482\n", "2016-08-02    14481\n", "2018-01-06    14459\n", "2016-02-02    14455\n", "2017-12-03    14451\n", "2016-07-18    14449\n", "2017-04-07    14428\n", "2016-01-20    14423\n", "2016-07-21    14402\n", "2016-03-02    14396\n", "2016-07-13    14396\n", "2017-11-11    14396\n", "2015-12-01    14365\n", "2016-12-22    14348\n", "2017-09-23    14321\n", "2016-08-01    14316\n", "2016-09-15    14305\n", "2016-04-04    14302\n", "2018-03-17    14251\n", "2018-01-07    14213\n", "2015-10-27    14208\n", "2018-01-20    14208\n", "2017-07-23    14204\n", "2016-01-19    14203\n", "2016-10-28    14201\n", "2016-10-14    14181\n", "2017-12-10    14162\n", "2016-02-23    14148\n", "2015-12-17    14148\n", "2015-11-03    14082\n", "2016-03-01    14080\n", "2017-08-19    14071\n", "2018-03-10    14070\n", "2017-11-25    14058\n", "2017-05-29    14042\n", "2015-11-10    14040\n", "2016-04-28    14022\n", "2016-08-03    14018\n", "2018-02-11    13977\n", "2017-01-13    13977\n", "2016-04-06    13950\n", "2016-02-11    13889\n", "2016-11-04    13884\n", "2017-11-19    13877\n", "2015-11-23    13865\n", "2016-03-03    13861\n", "2016-12-09    13842\n", "2018-01-27    13830\n", "2015-06-01    13823\n", "2017-05-12    13814\n", "2015-05-28    13813\n", "2017-11-04    13797\n", "2017-11-12    13796\n", "2015-11-19    13770\n", "2015-11-18    13737\n", "2016-10-06    13726\n", "2017-07-30    13712\n", "2018-03-25    13695\n", "2018-12-23    13671\n", "2018-02-10    13665\n", "2014-12-22    13640\n", "2016-01-13    13637\n", "2016-06-14    13636\n", "2016-04-20    13632\n", "2017-10-14    13617\n", "2016-01-14    13613\n", "2016-09-22    13577\n", "2016-06-16    13556\n", "2018-03-24    13533\n", "2017-05-20    13498\n", "2017-11-05    13488\n", "2016-01-05    13484\n", "2017-03-31    13469\n", "2018-03-11    13447\n", "2017-02-03    13397\n", "2018-01-28    13391\n", "2014-07-24    13382\n", "2017-06-24    13370\n", "2016-07-14    13361\n", "2015-12-22    13357\n", "2014-12-31    13356\n", "2016-01-21    13324\n", "2016-01-27    13298\n", "2016-07-22    13278\n", "2016-07-28    13274\n", "2015-05-06    13254\n", "2017-12-16    13223\n", "2016-08-22    13213\n", "2016-08-23    13206\n", "2018-02-25    13173\n", "2016-02-17    13160\n", "2016-02-24    13158\n", "2017-04-14    13154\n", "2016-01-28    13136\n", "2015-03-18    13126\n", "2017-03-24    13124\n", "2018-03-18    13117\n", "2016-04-08    13109\n", "2017-08-12    13105\n", "2015-04-15    13102\n", "2016-02-10    13092\n", "2017-10-07    13090\n", "2017-07-16    13072\n", "2018-01-14    13069\n", "2016-06-24    13061\n", "2016-08-04    13057\n", "2015-08-27    13030\n", "2014-07-23    13021\n", "2016-08-12    13002\n", "2016-04-21    12999\n", "2017-05-21    12998\n", "2018-01-21    12982\n", "2015-04-22    12955\n", "2016-12-16    12953\n", "2017-12-17    12949\n", "2017-09-09    12941\n", "2017-09-17    12940\n", "2015-12-21    12933\n", "2017-09-02    12933\n", "2017-03-17    12903\n", "2017-08-20    12894\n", "2017-10-15    12887\n", "2016-05-10    12879\n", "2015-12-30    12874\n", "2017-09-30    12873\n", "2017-10-21    12869\n", "2017-09-24    12855\n", "2017-10-01    12855\n", "2017-07-04    12844\n", "2018-02-03    12844\n", "2017-07-01    12799\n", "2017-06-17    12795\n", "2016-01-07    12794\n", "2018-03-04    12785\n", "2018-02-17    12763\n", "2016-02-29    12725\n", "2018-03-03    12714\n", "2015-07-14    12712\n", "2016-02-04    12692\n", "2016-02-03    12690\n", "2014-09-24    12689\n", "2015-12-14    12657\n", "2016-09-09    12647\n", "2017-01-02    12641\n", "2017-06-25    12622\n", "2016-03-19    12621\n", "2017-10-08    12613\n", "2017-03-10    12613\n", "2018-02-24    12603\n", "2016-09-29    12596\n", "2016-11-23    12596\n", "2017-08-13    12562\n", "2016-02-25    12558\n", "2016-05-05    12543\n", "2017-12-30    12521\n", "2018-11-22    12518\n", "2017-08-26    12507\n", "2018-01-01    12502\n", "2017-02-10    12399\n", "2016-03-11    12390\n", "2015-04-08    12388\n", "2015-12-09    12361\n", "2018-03-31    12294\n", "2016-01-25    12284\n", "2016-08-26    12258\n", "2015-01-14    12254\n", "2017-08-05    12251\n", "2017-10-28    12238\n", "2016-07-01    12237\n", "2018-02-18    12230\n", "2017-06-10    12220\n", "2017-09-10    12217\n", "2016-09-02    12217\n", "2017-10-22    12174\n", "2016-03-31    12159\n", "2017-01-27    12111\n", "2015-03-25    12098\n", "2016-01-11    12092\n", "2015-12-07    12086\n", "2017-01-20    12070\n", "2017-06-11    12063\n", "2016-05-11    12038\n", "2015-12-02    12023\n", "2016-01-06    12017\n", "2015-04-29    12016\n", "2017-08-27    12009\n", "2016-02-22    12007\n", "2018-04-01    11974\n", "2017-05-27    11969\n", "2017-07-02    11943\n", "2015-07-15    11932\n", "2016-11-11    11920\n", "2015-10-20    11906\n", "2017-10-29    11896\n", "2016-08-19    11893\n", "2016-02-08    11891\n", "2017-09-03    11824\n", "2015-07-07    11786\n", "2016-07-29    11775\n", "2016-10-21    11719\n", "2017-02-17    11682\n", "2015-09-23    11681\n", "2015-10-06    11664\n", "2016-07-15    11634\n", "2015-11-20    11630\n", "2016-01-04    11590\n", "2015-12-10    11586\n", "2015-11-04    11567\n", "2017-01-07    11566\n", "2015-02-18    11559\n", "2015-10-28    11525\n", "2015-11-30    11510\n", "2015-10-07    11492\n", "2015-12-03    11459\n", "2015-09-29    11433\n", "2017-05-06    11428\n", "2017-02-24    11411\n", "2015-09-02    11405\n", "2015-10-21    11403\n", "2015-10-19    11400\n", "2017-08-06    11395\n", "2015-11-05    11394\n", "2015-09-30    11373\n", "2017-06-18    11355\n", "2018-02-04    11305\n", "2015-09-28    11280\n", "2015-10-22    11272\n", "2014-09-19    11254\n", "2015-11-11    11250\n", "2015-08-28    11190\n", "2016-10-07    11186\n", "2016-03-04    11184\n", "2016-02-01    11180\n", "2016-08-05    11180\n", "2018-12-25    11173\n", "2016-06-13    11081\n", "2014-10-08    11075\n", "2016-04-22    11067\n", "2016-04-29    11032\n", "2015-11-16    11032\n", "2016-03-25    11031\n", "2016-09-30    11009\n", "2015-06-30    11005\n", "2014-07-09    10988\n", "2017-04-29    10971\n", "2017-05-07    10955\n", "2016-01-18    10940\n", "2015-10-05    10903\n", "2017-04-30    10863\n", "2015-10-29    10862\n", "2017-02-04    10817\n", "2015-10-01    10780\n", "2015-10-14    10774\n", "2014-10-22    10770\n", "2015-10-13    10760\n", "2015-11-12    10735\n", "2015-05-12    10731\n", "2017-04-22    10729\n", "2016-05-12    10726\n", "2015-07-08    10703\n", "2017-05-28    10677\n", "2016-01-15    10673\n", "2016-01-22    10554\n", "2014-10-01    10502\n", "2016-09-16    10488\n", "2016-09-23    10475\n", "2016-07-09    10433\n", "2015-10-16    10430\n", "2015-10-08    10428\n", "2016-05-06    10425\n", "2015-09-03    10425\n", "2016-11-25    10417\n", "2016-02-15    10410\n", "2017-12-23    10409\n", "2017-03-04    10407\n", "2017-01-14    10374\n", "2016-04-15    10359\n", "2016-01-08    10354\n", "2015-09-24    10307\n", "2015-06-29    10302\n", "2017-04-23    10285\n", "2016-02-12    10269\n", "2016-12-03    10259\n", "2016-03-12    10208\n", "2015-01-28    10182\n", "2015-08-11    10182\n", "2014-07-02    10128\n", "2016-04-09    10120\n", "2016-02-19    10086\n", "2017-04-09    10073\n", "2017-04-08    10068\n", "2017-01-08    10039\n", "2015-08-26    10023\n", "2016-04-01    10015\n", "2017-02-25     9972\n", "2016-11-19     9958\n", "2016-02-05     9928\n", "2014-06-25     9923\n", "2016-12-10     9914\n", "2017-04-01     9868\n", "2015-06-03     9848\n", "2015-08-12     9843\n", "2015-07-16     9821\n", "2015-06-02     9806\n", "2017-11-23     9767\n", "2014-06-04     9738\n", "2015-05-15     9726\n", "2016-11-12     9705\n", "2015-11-02     9684\n", "2017-03-18     9660\n", "2016-06-17     9640\n", "2017-05-13     9623\n", "2015-10-12     9617\n", "2017-03-11     9617\n", "2016-07-23     9598\n", "2017-02-11     9587\n", "2015-08-10     9582\n", "2015-10-26     9563\n", "2016-11-27     9499\n", "2016-12-17     9485\n", "2016-11-20     9443\n", "2015-12-23     9413\n", "2017-04-02     9408\n", "2015-09-15     9403\n", "2017-03-25     9402\n", "2014-07-15     9391\n", "2017-01-21     9386\n", "2014-11-05     9380\n", "2016-04-10     9359\n", "2016-03-20     9351\n", "2017-03-05     9346\n", "2016-08-27     9337\n", "2016-07-24     9331\n", "2015-11-25     9327\n", "2017-04-15     9323\n", "2014-07-07     9319\n", "2016-01-29     9296\n", "2015-11-09     9277\n", "2016-07-10     9268\n", "2014-12-29     9259\n", "2014-09-23     9255\n", "2017-03-26     9253\n", "2015-12-18     9249\n", "2016-08-13     9227\n", "2015-05-18     9193\n", "2015-07-09     9142\n", "2015-07-21     9141\n", "2014-07-08     9137\n", "2015-12-11     9126\n", "2017-03-19     9122\n", "2014-07-14     9116\n", "2016-08-14     9109\n", "2016-12-04     9104\n", "2015-04-01     9100\n", "2016-12-11     9094\n", "2014-07-22     9091\n", "2016-10-15     9087\n", "2017-02-12     9021\n", "2015-05-13     9018\n", "2015-09-10     9016\n", "2016-11-06     8980\n", "2015-07-02     8972\n", "2016-10-29     8970\n", "2016-11-26     8943\n", "2015-06-25     8937\n", "2014-07-21     8922\n", "2015-11-13     8916\n", "2015-07-20     8904\n", "2014-07-16     8894\n", "2016-11-05     8888\n", "2016-06-08     8877\n", "2015-05-19     8866\n", "2016-04-02     8833\n", "2016-09-05     8818\n", "2017-01-15     8811\n", "2015-02-25     8810\n", "2016-04-30     8802\n", "2015-09-14     8802\n", "2015-08-06     8796\n", "2015-08-13     8782\n", "2014-12-30     8779\n", "2017-12-31     8779\n", "2015-01-05     8762\n", "2016-07-31     8743\n", "2016-12-23     8737\n", "2017-03-12     8735\n", "2016-08-20     8729\n", "2015-06-04     8712\n", "2017-01-28     8710\n", "2016-08-28     8703\n", "2017-01-22     8668\n", "2016-10-22     8663\n", "2015-07-22     8662\n", "2015-08-25     8652\n", "2016-07-30     8644\n", "2015-06-09     8640\n", "2016-06-25     8630\n", "2016-08-21     8617\n", "2016-03-13     8616\n", "2016-02-06     8597\n", "2015-08-05     8595\n", "2016-07-02     8589\n", "2016-05-01     8588\n", "2017-02-18     8554\n", "2016-07-16     8551\n", "2014-09-04     8538\n", "2016-02-26     8519\n", "2014-06-11     8517\n", "2015-08-18     8510\n", "2016-10-23     8506\n", "2015-05-11     8491\n", "2016-07-17     8486\n", "2016-12-26     8485\n", "2016-01-23     8465\n", "2015-10-23     8465\n", "2015-12-04     8457\n", "2016-10-01     8440\n", "2014-10-15     8408\n", "2015-06-08     8406\n", "2015-08-17     8393\n", "2015-01-21     8388\n", "2016-10-16     8385\n", "2014-08-19     8380\n", "2015-05-26     8377\n", "2015-08-19     8352\n", "2014-09-03     8349\n", "2015-03-16     8342\n", "2014-04-23     8325\n", "2017-01-29     8320\n", "2015-09-01     8316\n", "2015-09-16     8284\n", "2015-07-27     8274\n", "2014-05-27     8271\n", "2015-03-17     8262\n", "2014-07-17     8261\n", "2014-09-22     8253\n", "2016-10-30     8244\n", "2015-11-21     8236\n", "2015-08-07     8231\n", "2016-12-18     8230\n", "2014-09-25     8216\n", "2016-01-16     8216\n", "2016-06-26     8202\n", "2015-05-05     8193\n", "2014-08-06     8187\n", "2016-04-23     8183\n", "2014-07-10     8178\n", "2016-06-09     8168\n", "2014-07-28     8161\n", "2016-10-02     8152\n", "2015-03-11     8139\n", "2014-09-02     8120\n", "2016-09-03     8113\n", "2015-06-24     8096\n", "2017-04-16     8095\n", "2016-01-17     8094\n", "2014-10-20     8087\n", "2015-04-14     8082\n", "2016-03-05     8075\n", "2017-05-14     8067\n", "2017-02-05     8054\n", "2015-04-13     8037\n", "2016-04-24     8035\n", "2015-08-31     8019\n", "2015-08-04     7999\n", "2014-05-14     7994\n", "2016-02-13     7993\n", "2015-10-02     7981\n", "2014-07-29     7975\n", "2015-01-06     7966\n", "2015-07-17     7963\n", "2016-01-24     7956\n", "2015-09-04     7935\n", "2016-05-13     7931\n", "2015-07-10     7929\n", "2015-05-20     7928\n", "2015-05-27     7892\n", "2015-04-21     7885\n", "2015-10-30     7878\n", "2015-06-10     7877\n", "2014-08-04     7876\n", "2016-01-09     7833\n", "2017-02-19     7832\n", "2015-04-07     7824\n", "2015-03-04     7819\n", "2014-05-07     7810\n", "2014-03-25     7806\n", "2014-04-09     7788\n", "2016-08-06     7771\n", "2014-08-18     7770\n", "2015-01-08     7768\n", "2016-06-11     7764\n", "2015-10-09     7732\n", "2015-11-27     7720\n", "2015-02-04     7710\n", "2016-08-07     7710\n", "2016-04-03     7705\n", "2015-08-24     7703\n", "2015-04-23     7700\n", "2014-07-01     7700\n", "2014-06-26     7688\n", "2015-01-29     7687\n", "2016-03-26     7678\n", "2015-09-21     7676\n", "2016-07-04     7659\n", "2015-11-06     7633\n", "2015-01-13     7613\n", "2015-07-28     7611\n", "2015-06-05     7611\n", "2014-12-23     7601\n", "2015-09-25     7599\n", "2014-08-27     7590\n", "2014-07-25     7587\n", "2015-09-17     7567\n", "2014-06-30     7566\n", "2014-08-20     7563\n", "2015-07-23     7557\n", "2015-01-27     7543\n", "2016-09-24     7537\n", "2014-08-25     7530\n", "2015-04-16     7524\n", "2015-08-20     7523\n", "2017-02-26     7522\n", "2014-08-26     7512\n", "2015-08-14     7505\n", "2014-10-06     7505\n", "2016-01-30     7498\n", "2014-07-30     7495\n", "2015-04-20     7494\n", "2015-01-15     7491\n", "2015-05-07     7479\n", "2015-01-02     7478\n", "2014-11-03     7443\n", "2016-09-11     7443\n", "2016-02-20     7427\n", "2016-10-08     7407\n", "2016-05-16     7407\n", "2016-01-10     7406\n", "2014-10-21     7406\n", "2014-10-07     7397\n", "2015-04-09     7389\n", "2014-08-29     7375\n", "2014-09-08     7374\n", "2016-01-31     7365\n", "2015-03-19     7361\n", "2014-10-28     7353\n", "2015-08-03     7334\n", "2016-09-17     7327\n", "2016-02-21     7314\n", "2015-03-23     7308\n", "2014-11-04     7296\n", "2016-05-07     7280\n", "2015-02-11     7274\n", "2014-09-30     7273\n", "2016-11-13     7271\n", "2014-06-24     7267\n", "2015-02-24     7264\n", "2016-04-16     7242\n", "2015-06-11     7228\n", "2014-07-18     7219\n", "2016-04-17     7201\n", "2015-07-29     7199\n", "2014-03-11     7180\n", "2016-12-31     7173\n", "2015-11-22     7170\n", "2015-06-16     7165\n", "2015-01-22     7159\n", "2015-05-04     7158\n", "2014-08-11     7154\n", "2015-01-20     7153\n", "2014-08-28     7141\n", "2015-04-27     7138\n", "2015-06-27     7137\n", "2014-09-29     7116\n", "2014-08-21     7108\n", "2015-06-15     7108\n", "2014-10-14     7088\n", "2015-01-12     7077\n", "2014-05-28     7075\n", "2016-09-04     7073\n", "2015-04-30     7064\n", "2016-05-17     7051\n", "2015-09-08     7049\n", "2016-07-03     7036\n", "2014-08-07     7035\n", "2014-10-02     7024\n", "2017-01-01     7018\n", "2015-01-26     7008\n", "2015-07-30     6999\n", "2014-09-05     6988\n", "2014-11-06     6973\n", "2014-07-11     6955\n", "2015-09-09     6946\n", "2015-04-06     6925\n", "2016-03-06     6920\n", "2015-06-17     6919\n", "2016-09-25     6914\n", "2015-03-24     6912\n", "2015-03-10     6882\n", "2016-01-02     6861\n", "2015-05-21     6859\n", "2014-07-31     6852\n", "2014-04-02     6846\n", "2015-05-29     6836\n", "2014-08-12     6834\n", "2014-07-03     6830\n", "2015-02-23     6821\n", "2016-09-18     6809\n", "2014-10-27     6807\n", "2015-12-19     6805\n", "2015-06-23     6803\n", "2014-08-13     6793\n", "2015-02-03     6781\n", "2015-12-31     6727\n", "2015-03-09     6726\n", "2016-01-03     6720\n", "2015-04-28     6695\n", "2016-10-09     6676\n", "2017-12-25     6675\n", "2015-06-22     6674\n", "2014-06-23     6665\n", "2014-11-20     6658\n", "2015-11-14     6655\n", "2014-10-29     6645\n", "2016-02-14     6590\n", "2014-10-23     6557\n", "2014-05-20     6556\n", "2015-12-12     6551\n", "2014-10-09     6550\n", "2015-04-10     6534\n", "2016-02-07     6529\n", "2015-03-26     6528\n", "2014-05-29     6525\n", "2015-08-21     6509\n", "2014-08-22     6506\n", "2015-11-29     6499\n", "2015-02-19     6498\n", "2015-07-03     6495\n", "2014-06-05     6482\n", "2014-10-16     6478\n", "2014-09-15     6431\n", "2017-12-24     6424\n", "2016-06-12     6414\n", "2013-10-29     6408\n", "2014-10-13     6395\n", "2014-04-16     6379\n", "2013-12-09     6351\n", "2016-06-18     6341\n", "2015-01-09     6340\n", "2015-03-30     6339\n", "2015-11-28     6329\n", "2014-08-14     6329\n", "2016-05-23     6319\n", "2015-03-02     6305\n", "2015-07-24     6299\n", "2014-04-15     6284\n", "2015-02-02     6269\n", "2015-03-12     6260\n", "2014-06-27     6253\n", "2016-11-24     6247\n", "2016-02-27     6239\n", "2016-05-18     6234\n", "2015-03-31     6219\n", "2014-09-16     6210\n", "2015-02-26     6210\n", "2015-05-08     6176\n", "2016-05-08     6126\n", "2014-05-19     6125\n", "2014-09-09     6120\n", "2015-12-13     6118\n", "2015-07-11     6117\n", "2015-05-16     6114\n", "2015-06-18     6110\n", "2014-11-17     6101\n", "2015-01-23     6098\n", "2015-08-08     6095\n", "2014-09-26     6090\n", "2014-09-17     6077\n", "2014-12-09     6058\n", "2014-08-08     6057\n", "2014-08-01     6051\n", "2015-09-18     6031\n", "2014-11-10     6024\n", "2016-02-28     6024\n", "2015-10-17     6004\n", "2014-04-08     5997\n", "2015-06-28     5991\n", "2016-05-19     5982\n", "2015-02-17     5969\n", "2014-06-10     5960\n", "2015-04-17     5959\n", "2014-04-14     5948\n", "2015-04-02     5932\n", "2014-08-15     5922\n", "2015-12-27     5920\n", "2015-03-03     5915\n", "2014-12-11     5911\n", "2015-12-20     5900\n", "2014-12-04     5892\n", "2015-01-16     5891\n", "2015-10-03     5890\n", "2016-06-07     5890\n", "2014-04-22     5876\n", "2015-12-05     5869\n", "2015-02-09     5862\n", "2015-10-24     5861\n", "2016-05-31     5851\n", "2015-11-07     5848\n", "2014-12-08     5847\n", "2015-03-20     5845\n", "2014-06-19     5844\n", "2016-05-24     5843\n", "2014-05-21     5842\n", "2015-02-10     5833\n", "2014-12-16     5821\n", "2014-10-30     5819\n", "2015-06-12     5810\n", "2016-05-15     5795\n", "2014-11-07     5794\n", "2014-05-15     5792\n", "2015-01-19     5784\n", "2014-01-22     5783\n", "2015-09-11     5775\n", "2014-01-23     5763\n", "2016-06-06     5763\n", "2015-11-15     5761\n", "2015-07-31     5752\n", "2014-10-03     5751\n", "2015-07-18     5739\n", "2014-06-09     5737\n", "2015-03-13     5727\n", "2015-01-30     5722\n", "2014-06-17     5714\n", "2014-06-16     5704\n", "2015-04-24     5659\n", "2014-06-03     5656\n", "2016-05-14     5635\n", "2014-06-12     5625\n", "2014-10-10     5615\n", "2014-12-01     5613\n", "2016-06-01     5595\n", "2015-12-06     5581\n", "2014-09-18     5581\n", "2013-12-26     5572\n", "2014-11-11     5554\n", "2015-03-05     5552\n", "2015-05-22     5544\n", "2015-11-08     5538\n", "2014-12-02     5529\n", "2015-11-01     5528\n", "2014-11-18     5520\n", "2014-12-03     5519\n", "2015-08-29     5519\n", "2014-04-17     5508\n", "2014-11-12     5507\n", "2015-07-12     5506\n", "2014-04-21     5505\n", "2015-01-03     5505\n", "2016-01-01     5494\n", "2015-10-10     5488\n", "2014-05-13     5488\n", "2014-07-19     5481\n", "2015-09-26     5479\n", "2014-06-02     5476\n", "2015-10-18     5448\n", "2014-12-15     5441\n", "2016-05-25     5439\n", "2014-08-30     5432\n", "2014-11-19     5432\n", "2014-12-12     5431\n", "2014-12-10     5424\n", "2014-05-22     5418\n", "2014-06-20     5409\n", "2016-06-19     5396\n", "2015-02-20     5385\n", "2015-02-05     5365\n", "2015-01-01     5364\n", "2014-03-26     5356\n", "2014-12-17     5349\n", "2015-06-19     5346\n", "2015-10-04     5344\n", "2014-05-06     5330\n", "2013-12-10     5325\n", "2014-03-13     5325\n", "2014-04-07     5322\n", "2016-06-02     5320\n", "2014-05-12     5319\n", "2015-06-06     5303\n", "2014-10-17     5301\n", "2014-11-24     5291\n", "2015-05-17     5290\n", "2014-05-05     5276\n", "2014-12-05     5270\n", "2014-06-18     5265\n", "2016-05-20     5255\n", "2015-10-31     5253\n", "2014-07-26     5239\n", "2014-10-24     5236\n", "2015-08-09     5234\n", "2015-05-01     5220\n", "2015-08-15     5211\n", "2015-02-27     5210\n", "2015-07-19     5206\n", "2016-05-26     5203\n", "2014-11-13     5193\n", "2014-03-12     5188\n", "2015-10-25     5185\n", "2014-09-10     5138\n", "2015-02-12     5116\n", "2014-04-10     5114\n", "2015-12-26     5112\n", "2014-05-16     5101\n", "2015-03-27     5099\n", "2014-02-24     5097\n", "2014-07-27     5092\n", "2015-10-11     5073\n", "2015-08-16     5071\n", "2014-04-24     5051\n", "2015-03-06     5042\n", "2015-02-16     5037\n", "2014-02-26     5030\n", "2015-08-30     5025\n", "2014-09-01     5008\n", "2016-03-27     4995\n", "2015-03-14     4994\n", "2015-09-27     4990\n", "2016-09-10     4960\n", "2014-02-03     4926\n", "2014-01-07     4912\n", "2014-09-06     4906\n", "2015-01-04     4899\n", "2015-05-30     4881\n", "2014-03-17     4879\n", "2014-07-13     4865\n", "2014-03-24     4864\n", "2014-06-13     4863\n", "2014-05-08     4856\n", "2014-03-19     4844\n", "2015-06-07     4837\n", "2014-04-28     4836\n", "2013-10-04     4836\n", "2015-01-10     4825\n", "2014-06-06     4820\n", "2014-03-14     4804\n", "2014-11-14     4800\n", "2014-08-24     4790\n", "2014-04-18     4779\n", "2014-07-12     4772\n", "2014-03-18     4771\n", "2014-01-02     4771\n", "2014-02-04     4769\n", "2014-08-23     4765\n", "2014-03-10     4747\n", "2014-07-06     4744\n", "2016-12-24     4736\n", "2014-11-21     4735\n", "2014-04-03     4734\n", "2014-08-02     4733\n", "2014-09-11     4732\n", "2014-11-25     4725\n", "2013-09-06     4722\n", "2014-07-20     4712\n", "2014-12-18     4708\n", "2015-04-11     4690\n", "2015-08-22     4655\n", "2014-03-20     4645\n", "2015-01-24     4641\n", "2013-08-19     4625\n", "2015-05-31     4611\n", "2014-05-30     4611\n", "2014-04-01     4590\n", "2014-06-29     4576\n", "2015-01-11     4574\n", "2015-05-09     4572\n", "2015-09-19     4569\n", "2015-02-13     4564\n", "2014-03-31     4560\n", "2016-06-03     4540\n", "2014-09-20     4517\n", "2014-08-17     4512\n", "2014-08-16     4510\n", "2015-07-05     4505\n", "2014-03-27     4498\n", "2014-09-07     4493\n", "2015-04-03     4491\n", "2014-08-03     4478\n", "2015-08-23     4477\n", "2015-09-07     4475\n", "2013-12-11     4455\n", "2014-01-21     4439\n", "2014-09-21     4436\n", "2014-06-28     4436\n", "2014-08-09     4404\n", "2013-12-12     4401\n", "2015-03-21     4397\n", "2014-09-27     4390\n", "2015-03-15     4370\n", "2015-07-25     4365\n", "2014-07-05     4354\n", "2015-09-05     4353\n", "2014-04-29     4339\n", "2015-02-06     4338\n", "2015-07-26     4333\n", "2015-12-24     4305\n", "2014-01-27     4296\n", "2014-01-14     4284\n", "2015-05-25     4279\n", "2014-02-05     4263\n", "2014-11-08     4254\n", "2015-03-22     4251\n", "2015-02-21     4249\n", "2014-04-25     4248\n", "2014-10-31     4236\n", "2014-02-27     4232\n", "2014-01-13     4228\n", "2014-10-11     4227\n", "2015-04-12     4223\n", "2014-08-10     4223\n", "2014-10-04     4212\n", "2015-09-12     4212\n", "2014-03-05     4189\n", "2014-02-25     4172\n", "2015-04-18     4170\n", "2014-02-14     4166\n", "2014-01-28     4159\n", "2015-06-13     4155\n", "2014-09-12     4144\n", "2015-08-01     4141\n", "2015-03-07     4135\n", "2015-08-02     4126\n", "2015-01-31     4122\n", "2016-05-27     4122\n", "2015-09-20     4121\n", "2014-05-23     4121\n", "2014-08-31     4121\n", "2015-01-17     4116\n", "2014-04-30     4109\n", "2014-01-15     4105\n", "2013-12-30     4099\n", "2014-05-09     4095\n", "2014-03-06     4080\n", "2014-10-18     4078\n", "2014-09-28     4076\n", "2014-04-11     4061\n", "2013-10-01     4060\n", "2016-12-25     4054\n", "2013-10-28     4046\n", "2013-09-30     4042\n", "2014-06-21     4034\n", "2015-04-19     4014\n", "2014-02-19     4009\n", "2014-05-01     4007\n", "2014-12-19     3996\n", "2015-04-25     3994\n", "2015-02-28     3993\n", "2015-09-13     3977\n", "2015-06-14     3971\n", "2014-02-18     3968\n", "2013-10-30     3961\n", "2014-11-02     3943\n", "2014-11-09     3935\n", "2013-12-16     3933\n", "2014-10-05     3926\n", "2015-01-25     3910\n", "2016-05-22     3909\n", "2014-03-21     3908\n", "2014-01-16     3895\n", "2014-11-26     3894\n", "2014-01-08     3889\n", "2015-03-01     3879\n", "2015-05-23     3876\n", "2014-03-03     3853\n", "2014-12-06     3853\n", "2015-11-26     3847\n", "2014-01-03     3837\n", "2014-10-25     3825\n", "2014-10-26     3814\n", "2014-11-01     3805\n", "2014-06-22     3800\n", "2014-10-12     3796\n", "2014-10-19     3767\n", "2015-04-26     3764\n", "2015-02-22     3751\n", "2015-03-28     3741\n", "2014-01-24     3727\n", "2014-01-06     3724\n", "2014-03-04     3721\n", "2013-12-27     3720\n", "2013-12-05     3711\n", "2015-01-18     3708\n", "2014-12-26     3697\n", "2014-04-04     3689\n", "2015-05-02     3689\n", "2013-09-03     3688\n", "2013-11-04     3674\n", "2015-05-10     3671\n", "2013-08-20     3661\n", "2013-08-26     3653\n", "2013-11-05     3647\n", "2014-01-17     3642\n", "2014-11-22     3621\n", "2013-12-17     3619\n", "2015-05-03     3617\n", "2014-05-17     3614\n", "2013-06-12     3609\n", "2015-06-20     3592\n", "2013-09-09     3582\n", "2014-01-09     3554\n", "2015-03-29     3551\n", "2015-07-04     3548\n", "2013-07-15     3548\n", "2014-02-06     3546\n", "2014-03-07     3538\n", "2015-03-08     3536\n", "2014-02-10     3536\n", "2015-04-04     3534\n", "2013-08-27     3529\n", "2016-06-04     3527\n", "2013-09-04     3526\n", "2013-10-16     3526\n", "2014-03-28     3520\n", "2014-11-28     3517\n", "2014-02-22     3514\n", "2016-05-21     3507\n", "2013-10-02     3479\n", "2015-06-21     3468\n", "2013-06-11     3466\n", "2014-01-29     3466\n", "2015-09-06     3459\n", "2015-05-24     3448\n", "2014-11-30     3439\n", "2013-10-03     3437\n", "2013-10-15     3436\n", "2014-11-15     3435\n", "2014-03-15     3428\n", "2013-10-21     3425\n", "2014-06-14     3416\n", "2013-10-08     3413\n", "2014-12-27     3393\n", "2013-12-13     3383\n", "2014-05-18     3379\n", "2013-12-18     3378\n", "2014-11-23     3372\n", "2014-07-04     3371\n", "2013-10-07     3370\n", "2014-11-16     3369\n", "2014-02-28     3358\n", "2014-12-28     3356\n", "2015-02-07     3349\n", "2016-06-05     3332\n", "2014-02-20     3330\n", "2013-11-12     3328\n", "2013-09-10     3316\n", "2014-09-14     3305\n", "2014-02-11     3304\n", "2014-01-20     3303\n", "2013-10-22     3297\n", "2014-02-21     3291\n", "2013-09-24     3278\n", "2014-12-13     3277\n", "2013-07-22     3260\n", "2013-07-11     3257\n", "2014-04-19     3254\n", "2014-12-07     3247\n", "2014-05-02     3239\n", "2016-05-30     3235\n", "2013-10-14     3217\n", "2014-01-30     3211\n", "2013-07-23     3209\n", "2013-06-25     3205\n", "2013-10-09     3198\n", "2013-06-24     3180\n", "2014-04-12     3173\n", "2014-09-13     3172\n", "2013-07-12     3164\n", "2013-09-11     3160\n", "2015-02-01     3152\n", "2014-12-14     3142\n", "2014-11-29     3140\n", "2014-03-16     3138\n", "2013-10-23     3136\n", "2015-02-14     3108\n", "2013-10-31     3105\n", "2015-02-08     3104\n", "2013-09-25     3103\n", "2014-12-20     3094\n", "2013-10-18     3083\n", "2013-07-16     3066\n", "2013-10-17     3065\n", "2014-05-31     3061\n", "2013-08-21     3060\n", "2015-02-15     3058\n", "2013-10-10     3052\n", "2013-11-01     3037\n", "2014-02-12     3032\n", "2014-04-13     3018\n", "2013-12-23     3016\n", "2013-09-17     3014\n", "2013-07-10     3008\n", "2013-09-18     2993\n", "2014-01-10     2992\n", "2013-07-17     2986\n", "2014-06-01     2978\n", "2013-09-23     2971\n", "2013-12-06     2970\n", "2013-08-28     2963\n", "2014-05-26     2957\n", "2014-06-07     2954\n", "2013-08-06     2950\n", "2014-05-24     2941\n", "2014-06-08     2936\n", "2014-06-15     2935\n", "2015-04-05     2935\n", "2013-08-05     2934\n", "2013-12-19     2926\n", "2014-02-07     2926\n", "2013-08-22     2921\n", "2013-11-06     2899\n", "2013-07-24     2890\n", "2013-09-16     2886\n", "2014-12-24     2880\n", "2013-06-13     2870\n", "2014-03-22     2869\n", "2014-04-26     2855\n", "2013-06-26     2847\n", "2013-10-24     2839\n", "2013-09-12     2837\n", "2013-07-08     2821\n", "2013-06-10     2821\n", "2014-04-27     2819\n", "2013-08-07     2813\n", "2013-04-22     2799\n", "2014-02-17     2790\n", "2013-07-01     2786\n", "2013-07-30     2774\n", "2013-09-19     2773\n", "2015-12-25     2764\n", "2013-09-05     2762\n", "2013-08-12     2755\n", "2013-07-29     2753\n", "2013-09-26     2746\n", "2016-05-28     2746\n", "2014-12-21     2737\n", "2013-04-23     2733\n", "2014-01-18     2730\n", "2013-08-14     2728\n", "2014-01-31     2727\n", "2013-04-29     2717\n", "2014-03-29     2715\n", "2013-07-02     2707\n", "2014-05-10     2704\n", "2013-05-28     2701\n", "2013-04-30     2697\n", "2016-05-29     2691\n", "2013-08-29     2686\n", "2013-06-03     2685\n", "2013-10-25     2681\n", "2013-12-20     2681\n", "2013-10-11     2677\n", "2013-09-27     2669\n", "2014-03-23     2653\n", "2013-06-04     2650\n", "2013-12-31     2644\n", "2013-01-14     2642\n", "2013-07-09     2635\n", "2013-07-31     2626\n", "2014-01-25     2612\n", "2013-07-18     2602\n", "2014-02-13     2581\n", "2013-08-15     2578\n", "2014-01-26     2565\n", "2013-12-28     2565\n", "2013-05-29     2556\n", "2013-08-13     2556\n", "2014-03-08     2551\n", "2014-03-01     2539\n", "2014-02-23     2537\n", "2013-04-24     2521\n", "2013-05-21     2520\n", "2013-06-18     2511\n", "2013-07-25     2509\n", "2013-05-20     2508\n", "2014-03-30     2507\n", "2013-05-06     2505\n", "2013-06-17     2502\n", "2014-04-05     2500\n", "2013-08-08     2476\n", "2013-08-23     2465\n", "2014-01-04     2459\n", "2013-06-05     2448\n", "2014-04-20     2438\n", "2013-09-20     2435\n", "2013-05-01     2406\n", "2013-01-15     2392\n", "2013-06-27     2391\n", "2013-08-01     2386\n", "2014-05-25     2381\n", "2014-03-09     2377\n", "2013-06-19     2377\n", "2014-04-06     2373\n", "2013-08-16     2368\n", "2013-05-22     2351\n", "2014-01-11     2349\n", "2013-07-13     2346\n", "2013-09-13     2343\n", "2013-12-07     2343\n", "2013-07-03     2343\n", "2013-02-26     2342\n", "2014-03-02     2341\n", "2013-12-14     2337\n", "2013-05-13     2333\n", "2014-05-04     2329\n", "2013-12-29     2323\n", "2013-05-14     2309\n", "2013-06-06     2308\n", "2014-02-15     2307\n", "2013-05-02     2303\n", "2013-07-19     2302\n", "2013-11-13     2294\n", "2013-05-30     2292\n", "2013-09-07     2282\n", "2014-05-11     2270\n", "2013-05-10     2266\n", "2014-02-08     2266\n", "2013-07-26     2263\n", "2013-06-14     2261\n", "2014-01-19     2257\n", "2013-11-02     2246\n", "2013-10-05     2234\n", "2013-08-09     2226\n", "2013-12-15     2219\n", "2014-05-03     2214\n", "2013-04-25     2213\n", "2013-05-15     2191\n", "2013-06-28     2185\n", "2013-08-30     2181\n", "2013-12-08     2181\n", "2013-05-07     2180\n", "2013-12-21     2175\n", "2013-07-05     2146\n", "2014-01-01     2144\n", "2014-01-12     2135\n", "2013-06-20     2127\n", "2014-01-05     2105\n", "2013-07-14     2097\n", "2013-04-15     2090\n", "2013-02-25     2081\n", "2014-02-09     2049\n", "2013-08-02     2046\n", "2013-06-23     2043\n", "2013-11-03     2038\n", "2013-10-12     2030\n", "2013-05-08     2000\n", "2013-02-27     1997\n", "2013-10-19     1985\n", "2013-06-07     1982\n", "2013-04-13     1981\n", "2013-03-18     1976\n", "2014-02-01     1967\n", "2013-05-16     1950\n", "2013-04-02     1941\n", "2013-09-08     1941\n", "2013-04-26     1916\n", "2013-10-06     1909\n", "2013-09-21     1901\n", "2013-04-16     1898\n", "2013-10-13     1894\n", "2014-02-16     1894\n", "2013-01-16     1888\n", "2013-11-11     1888\n", "2013-04-17     1886\n", "2013-03-04     1881\n", "2013-09-28     1870\n", "2013-09-02     1867\n", "2013-05-23     1856\n", "2013-03-05     1844\n", "2013-05-03     1836\n", "2012-10-15     1825\n", "2013-07-21     1823\n", "2013-10-20     1822\n", "2012-10-05     1820\n", "2012-11-20     1814\n", "2012-09-24     1811\n", "2013-06-21     1809\n", "2013-05-09     1808\n", "2013-07-20     1804\n", "2013-03-24     1803\n", "2013-12-22     1797\n", "2013-10-26     1796\n", "2013-04-18     1793\n", "2013-03-19     1790\n", "2013-03-26     1789\n", "2013-08-17     1786\n", "2013-05-31     1785\n", "2013-02-04     1785\n", "2013-11-07     1771\n", "2014-12-25     1765\n", "2013-09-29     1762\n", "2013-05-24     1753\n", "2013-04-03     1748\n", "2013-05-17     1746\n", "2013-11-08     1745\n", "2013-01-11     1744\n", "2013-03-25     1740\n", "2014-11-27     1737\n", "2013-02-05     1733\n", "2013-04-01     1729\n", "2013-01-13     1718\n", "2013-08-24     1707\n", "2013-12-24     1701\n", "2013-08-18     1697\n", "2013-04-11     1686\n", "2011-12-05     1684\n", "2013-07-27     1682\n", "2013-10-27     1681\n", "2012-12-04     1681\n", "2012-12-03     1675\n", "2013-08-10     1671\n", "2013-01-10     1670\n", "2013-04-19     1668\n", "2013-08-25     1665\n", "2013-03-17     1662\n", "2013-07-07     1661\n", "2013-04-10     1642\n", "2013-01-17     1638\n", "2013-01-03     1633\n", "2012-11-16     1619\n", "2012-10-04     1616\n", "2013-08-31     1615\n", "2013-08-11     1614\n", "2012-12-17     1610\n", "2013-03-20     1609\n", "2013-02-28     1608\n", "2013-05-04     1607\n", "2012-11-15     1603\n", "2013-04-12     1601\n", "2013-09-15     1601\n", "2013-09-14     1600\n", "2013-01-12     1600\n", "2013-04-08     1597\n", "2013-08-03     1595\n", "2012-10-30     1592\n", "2013-12-04     1587\n", "2013-03-06     1585\n", "2013-06-29     1576\n", "2013-09-22     1575\n", "2012-09-17     1573\n", "2014-02-02     1561\n", "2013-01-28     1560\n", "2013-07-28     1550\n", "2013-01-04     1549\n", "2013-04-09     1548\n", "2012-11-29     1546\n", "2012-10-29     1545\n", "2013-05-05     1545\n", "2013-01-09     1543\n", "2013-04-04     1539\n", "2012-12-12     1537\n", "2013-07-06     1537\n", "2012-11-27     1535\n", "2012-10-17     1524\n", "2012-11-26     1523\n", "2013-02-06     1500\n", "2013-03-27     1500\n", "2012-12-05     1499\n", "2012-12-11     1494\n", "2012-11-19     1491\n", "2013-04-14     1487\n", "2013-01-29     1486\n", "2013-06-01     1486\n", "2012-09-06     1478\n", "2012-10-02     1476\n", "2012-09-18     1472\n", "2013-06-15     1465\n", "2013-08-04     1462\n", "2013-02-12     1458\n", "2013-01-07     1456\n", "2013-03-01     1452\n", "2012-12-13     1441\n", "2013-03-12     1440\n", "2013-03-16     1439\n", "2013-06-09     1435\n", "2013-03-11     1432\n", "2012-12-18     1430\n", "2013-02-19     1429\n", "2013-01-08     1423\n", "2012-09-25     1420\n", "2012-06-04     1416\n", "2012-11-13     1415\n", "2012-12-27     1414\n", "2012-10-16     1412\n", "2013-07-04     1402\n", "2013-01-23     1395\n", "2012-11-14     1393\n", "2012-11-28     1392\n", "2012-10-10     1391\n", "2012-10-03     1390\n", "2012-09-26     1389\n", "2012-10-22     1388\n", "2012-12-14     1384\n", "2013-04-20     1382\n", "2013-01-02     1381\n", "2013-02-11     1381\n", "2013-06-02     1377\n", "2012-12-10     1377\n", "2013-06-08     1374\n", "2013-02-20     1370\n", "2012-10-08     1369\n", "2012-12-26     1367\n", "2013-05-27     1367\n", "2013-09-01     1366\n", "2013-03-21     1363\n", "2012-07-10     1361\n", "2012-10-01     1358\n", "2013-04-28     1350\n", "2013-05-19     1348\n", "2013-01-22     1347\n", "2012-11-30     1345\n", "2013-06-30     1344\n", "2013-01-18     1340\n", "2013-05-11     1339\n", "2013-02-13     1337\n", "2012-09-19     1334\n", "2013-02-21     1334\n", "2013-03-13     1331\n", "2013-06-22     1329\n", "2013-04-27     1323\n", "2012-12-06     1322\n", "2012-10-23     1319\n", "2013-06-16     1313\n", "2013-03-07     1311\n", "2012-07-11     1308\n", "2012-09-20     1305\n", "2013-01-24     1300\n", "2013-05-18     1300\n", "2012-12-07     1296\n", "2012-10-09     1296\n", "2011-11-21     1296\n", "2013-04-05     1295\n", "2012-10-24     1294\n", "2012-06-05     1294\n", "2013-03-22     1292\n", "2012-12-16     1292\n", "2013-04-21     1282\n", "2012-11-06     1280\n", "2012-12-28     1277\n", "2012-09-07     1274\n", "2012-12-19     1271\n", "2013-03-14     1270\n", "2012-07-17     1268\n", "2012-12-15     1266\n", "2013-01-30     1264\n", "2012-10-18     1260\n", "2013-03-28     1258\n", "2012-11-09     1255\n", "2012-10-11     1254\n", "2012-11-01     1254\n", "2012-07-09     1250\n", "2012-09-10     1249\n", "2012-11-07     1247\n", "2012-06-25     1246\n", "2011-11-28     1243\n", "2012-11-05     1241\n", "2012-11-12     1236\n", "2012-08-28     1233\n", "2013-03-08     1231\n", "2013-02-07     1229\n", "2012-06-06     1226\n", "2013-03-02     1226\n", "2012-08-14     1213\n", "2011-09-23     1213\n", "2012-10-25     1205\n", "2012-07-16     1200\n", "2012-09-14     1194\n", "2012-10-26     1190\n", "2012-05-15     1190\n", "2012-08-13     1188\n", "2012-08-20     1188\n", "2012-09-11     1181\n", "2012-08-21     1178\n", "2012-10-12     1178\n", "2012-06-26     1175\n", "2013-01-31     1174\n", "2013-03-15     1171\n", "2012-05-14     1169\n", "2012-10-13     1164\n", "2012-11-08     1163\n", "2013-02-22     1162\n", "2012-04-03     1159\n", "2012-10-31     1159\n", "2012-11-17     1158\n", "2013-05-25     1156\n", "2013-01-21     1152\n", "2012-04-17     1148\n", "2012-04-02     1147\n", "2013-02-18     1144\n", "2012-05-21     1139\n", "2012-07-24     1138\n", "2012-07-18     1137\n", "2012-09-27     1130\n", "2013-11-09     1129\n", "2012-06-07     1125\n", "2012-07-05     1125\n", "2011-07-25     1123\n", "2013-02-01     1122\n", "2012-06-13     1122\n", "2012-06-12     1119\n", "2011-11-18     1117\n", "2012-08-22     1117\n", "2013-11-10     1117\n", "2012-06-11     1116\n", "2012-07-12     1116\n", "2012-09-12     1112\n", "2012-04-09     1111\n", "2012-08-27     1107\n", "2012-04-18     1104\n", "2012-05-16     1104\n", "2013-01-25     1100\n", "2012-09-21     1095\n", "2012-09-13     1093\n", "2012-09-05     1092\n", "2012-05-22     1091\n", "2012-06-27     1091\n", "2011-11-17     1090\n", "2011-11-22     1088\n", "2013-03-29     1087\n", "2013-04-06     1083\n", "2012-08-08     1079\n", "2012-07-23     1079\n", "2012-07-30     1078\n", "2012-07-19     1074\n", "2012-09-04     1073\n", "2013-02-14     1073\n", "2013-02-08     1071\n", "2012-04-16     1070\n", "2012-07-31     1070\n", "2012-12-29     1070\n", "2012-10-19     1069\n", "2013-05-12     1066\n", "2011-12-06     1064\n", "2011-11-14     1060\n", "2012-04-04     1058\n", "2012-08-30     1057\n", "2012-07-25     1051\n", "2012-08-06     1046\n", "2012-04-10     1045\n", "2012-06-14     1041\n", "2013-01-05     1039\n", "2013-03-03     1038\n", "2012-12-20     1037\n", "2012-06-18     1036\n", "2012-11-18     1034\n", "2013-01-19     1027\n", "2012-01-04     1026\n", "2012-11-21     1024\n", "2012-07-06     1024\n", "2012-05-17     1023\n", "2013-12-25     1017\n", "2012-08-09     1016\n", "2012-11-10     1015\n", "2012-08-23     1013\n", "2012-08-29     1011\n", "2012-05-07     1011\n", "2012-05-09     1009\n", "2012-08-07     1008\n", "2012-11-02     1006\n", "2012-04-05      999\n", "2011-08-15      996\n", "2013-02-15      994\n", "2012-11-23      990\n", "2012-10-14      989\n", "2013-02-24      989\n", "2012-08-15      988\n", "2012-08-16      983\n", "2013-04-07      982\n", "2012-09-15      975\n", "2012-05-08      974\n", "2012-10-21      973\n", "2012-06-20      971\n", "2012-08-10      970\n", "2011-12-01      968\n", "2013-05-26      967\n", "2012-09-30      966\n", "2011-11-15      964\n", "2012-10-07      961\n", "2013-01-06      961\n", "2011-07-26      960\n", "2013-03-23      959\n", "2012-12-01      958\n", "2012-05-29      955\n", "2011-08-03      954\n", "2012-05-30      954\n", "2012-08-01      953\n", "2011-08-02      953\n", "2011-11-16      952\n", "2012-09-28      952\n", "2012-11-25      950\n", "2012-06-19      950\n", "2011-12-28      950\n", "2012-04-12      949\n", "2011-09-07      948\n", "2012-12-30      947\n", "2012-04-23      946\n", "2012-01-03      945\n", "2012-08-02      944\n", "2012-04-25      944\n", "2011-07-12      944\n", "2011-08-09      942\n", "2011-06-01      940\n", "2012-04-24      939\n", "2012-08-17      938\n", "2011-07-18      938\n", "2012-05-23      937\n", "2012-04-19      936\n", "2013-02-09      935\n", "2011-08-01      930\n", "2012-07-02      928\n", "2011-06-14      927\n", "2011-06-02      924\n", "2013-02-16      924\n", "2012-12-31      923\n", "2013-01-01      922\n", "2011-07-21      921\n", "2011-04-19      920\n", "2011-11-08      916\n", "2012-05-02      914\n", "2011-11-09      913\n", "2012-11-04      912\n", "2011-09-06      911\n", "2011-07-22      910\n", "2013-02-23      904\n", "2013-03-30      904\n", "2013-03-09      903\n", "2012-07-15      903\n", "2012-07-26      903\n", "2012-03-19      901\n", "2012-02-07      897\n", "2011-06-06      896\n", "2011-07-06      895\n", "2012-07-20      894\n", "2013-01-20      894\n", "2012-12-09      894\n", "2012-10-06      894\n", "2012-09-22      892\n", "2013-02-17      891\n", "2012-09-29      890\n", "2012-03-12      888\n", "2011-07-11      886\n", "2012-05-03      886\n", "2012-04-11      885\n", "2012-12-08      884\n", "2012-06-28      881\n", "2011-12-27      880\n", "2011-07-20      880\n", "2011-11-19      879\n", "2012-12-02      878\n", "2011-12-07      878\n", "2012-11-11      878\n", "2012-07-13      875\n", "2013-03-10      875\n", "2012-03-26      874\n", "2011-02-28      873\n", "2011-08-16      871\n", "2012-05-31      870\n", "2011-08-08      865\n", "2012-10-27      863\n", "2011-10-03      863\n", "2012-09-16      861\n", "2011-10-17      861\n", "2011-07-19      859\n", "2012-03-13      859\n", "2011-05-23      859\n", "2012-10-20      857\n", "2012-05-10      857\n", "2012-09-23      856\n", "2012-04-06      856\n", "2012-03-05      855\n", "2012-06-08      854\n", "2012-03-20      853\n", "2011-11-20      853\n", "2012-01-23      853\n", "2012-11-24      853\n", "2011-08-04      851\n", "2013-02-10      846\n", "2012-05-11      846\n", "2011-09-27      845\n", "2013-01-27      840\n", "2011-12-29      838\n", "2011-06-07      835\n", "2011-09-12      835\n", "2012-01-30      833\n", "2012-01-18      832\n", "2011-02-22      832\n", "2011-07-07      830\n", "2012-05-24      830\n", "2012-03-14      829\n", "2011-02-23      824\n", "2012-07-07      823\n", "2012-07-08      823\n", "2012-08-24      822\n", "2011-06-13      819\n", "2011-11-10      819\n", "2011-09-28      818\n", "2012-03-15      818\n", "2011-06-28      817\n", "2012-01-02      815\n", "2012-05-01      814\n", "2011-09-19      810\n", "2011-08-25      809\n", "2012-04-26      806\n", "2011-06-20      805\n", "2011-10-04      805\n", "2012-01-24      803\n", "2011-07-13      802\n", "2011-04-13      802\n", "2011-08-10      802\n", "2012-10-28      801\n", "2012-03-21      800\n", "2012-06-15      800\n", "2011-06-21      799\n", "2013-01-26      797\n", "2011-08-24      796\n", "2011-09-26      796\n", "2011-06-08      795\n", "2013-02-02      794\n", "2011-09-01      794\n", "2011-02-24      792\n", "2011-03-14      792\n", "2011-10-05      791\n", "2011-04-11      789\n", "2012-06-21      787\n", "2011-12-02      786\n", "2012-11-03      786\n", "2011-06-15      786\n", "2012-05-18      785\n", "2011-04-12      784\n", "2013-03-31      784\n", "2011-06-16      782\n", "2011-08-23      781\n", "2012-03-02      781\n", "2011-10-24      779\n", "2011-11-27      777\n", "2011-03-02      777\n", "2011-06-29      775\n", "2011-04-18      774\n", "2011-11-26      770\n", "2011-07-14      769\n", "2011-09-14      769\n", "2012-04-20      768\n", "2012-03-29      767\n", "2011-09-08      767\n", "2011-07-24      766\n", "2011-03-03      765\n", "2011-06-03      764\n", "2011-03-10      764\n", "2012-12-21      764\n", "2011-08-22      763\n", "2011-04-05      763\n", "2011-06-27      762\n", "2012-03-27      762\n", "2011-12-04      762\n", "2011-09-29      761\n", "2012-07-03      757\n", "2011-12-03      757\n", "2011-04-14      757\n", "2012-08-03      757\n", "2012-03-28      756\n", "2012-02-06      751\n", "2012-07-14      750\n", "2012-01-09      750\n", "2011-03-01      748\n", "2011-03-08      746\n", "2011-03-09      744\n", "2011-02-16      741\n", "2011-11-25      739\n", "2012-03-06      738\n", "2012-08-31      737\n", "2012-01-19      737\n", "2011-04-07      737\n", "2011-08-17      736\n", "2011-07-23      736\n", "2011-07-05      734\n", "2011-09-20      734\n", "2012-05-04      733\n", "2011-03-07      733\n", "2012-06-29      731\n", "2011-04-25      731\n", "2011-10-19      730\n", "2012-01-11      728\n", "2012-08-11      727\n", "2011-03-16      726\n", "2011-10-06      725\n", "2011-10-10      725\n", "2011-10-12      724\n", "2011-08-18      723\n", "2011-09-22      723\n", "2011-04-06      722\n", "2012-04-13      722\n", "2012-01-12      722\n", "2011-10-26      719\n", "2010-05-11      719\n", "2011-12-08      719\n", "2011-08-12      718\n", "2012-05-25      714\n", "2012-09-08      713\n", "2012-07-27      712\n", "2013-02-03      709\n", "2011-10-25      709\n", "2011-08-11      709\n", "2012-06-22      709\n", "2011-08-05      707\n", "2011-11-23      707\n", "2011-10-18      707\n", "2012-04-14      703\n", "2011-07-08      703\n", "2011-12-30      703\n", "2012-03-08      703\n", "2012-01-25      700\n", "2012-03-07      700\n", "2011-07-30      699\n", "2011-11-07      699\n", "2012-08-18      699\n", "2011-09-13      698\n", "2012-02-08      697\n", "2011-10-11      697\n", "2011-05-02      696\n", "2012-04-15      696\n", "2012-08-04      694\n", "2012-03-23      693\n", "2011-06-30      693\n", "2012-06-09      691\n", "2011-03-15      688\n", "2012-08-12      688\n", "2011-10-15      688\n", "2011-06-22      688\n", "2012-08-26      688\n", "2012-07-29      687\n", "2012-03-01      686\n", "2012-09-09      686\n", "2012-03-16      686\n", "2011-11-02      683\n", "2011-09-15      682\n", "2012-03-22      680\n", "2011-03-04      678\n", "2011-09-09      676\n", "2012-05-20      675\n", "2012-06-02      673\n", "2011-09-25      673\n", "2012-12-24      672\n", "2011-07-31      669\n", "2011-01-11      669\n", "2012-09-03      668\n", "2012-08-25      667\n", "2011-02-17      667\n", "2011-02-18      666\n", "2011-02-21      665\n", "2011-11-12      661\n", "2012-08-19      660\n", "2011-11-11      660\n", "2011-06-09      660\n", "2011-02-15      659\n", "2011-04-20      658\n", "2011-04-21      653\n", "2012-05-19      653\n", "2011-11-30      652\n", "2011-04-04      652\n", "2011-11-13      651\n", "2011-09-21      649\n", "2011-11-29      647\n", "2011-03-21      645\n", "2011-11-01      644\n", "2012-01-10      643\n", "2012-06-16      641\n", "2012-06-01      641\n", "2011-10-07      641\n", "2011-12-12      640\n", "2011-10-20      640\n", "2011-04-08      639\n", "2012-05-12      639\n", "2012-02-09      638\n", "2012-05-06      638\n", "2011-07-15      637\n", "2012-06-23      636\n", "2011-03-22      636\n", "2012-07-21      636\n", "2012-02-21      636\n", "2012-06-03      635\n", "2011-11-03      635\n", "2012-01-17      634\n", "2011-04-15      633\n", "2012-04-21      632\n", "2011-09-02      630\n", "2011-06-04      629\n", "2012-03-09      628\n", "2012-09-01      627\n", "2012-03-17      626\n", "2011-10-27      625\n", "2012-04-30      624\n", "2011-03-11      623\n", "2012-02-27      620\n", "2011-08-31      620\n", "2012-02-03      620\n", "2008-04-07      619\n", "2011-03-28      616\n", "2012-05-05      616\n", "2011-02-14      614\n", "2012-06-10      613\n", "2011-10-31      613\n", "2012-04-22      613\n", "2012-01-26      610\n", "2012-07-22      609\n", "2011-06-17      608\n", "2011-10-13      608\n", "2012-02-02      608\n", "2012-04-07      607\n", "2011-03-24      607\n", "2012-04-27      606\n", "2011-08-06      605\n", "2012-12-22      596\n", "2012-02-22      596\n", "2011-02-25      595\n", "2011-01-06      595\n", "2012-01-06      594\n", "2010-09-28      593\n", "2011-12-13      591\n", "2012-01-20      590\n", "2012-08-05      588\n", "2011-10-21      588\n", "2011-01-04      587\n", "2010-11-16      587\n", "2011-09-30      587\n", "2011-10-14      586\n", "2012-03-25      584\n", "2011-07-27      584\n", "2012-01-27      583\n", "2011-02-11      582\n", "2012-07-01      577\n", "2011-06-24      577\n", "2012-01-05      577\n", "2012-01-16      576\n", "2011-11-04      575\n", "2011-01-03      573\n", "2011-03-23      572\n", "2012-03-31      571\n", "2010-10-19      571\n", "2011-10-28      570\n", "2012-02-28      570\n", "2012-04-01      570\n", "2012-02-29      570\n", "2011-08-19      569\n", "2012-06-24      569\n", "2011-07-09      567\n", "2012-03-24      567\n", "2012-05-13      565\n", "2012-03-30      562\n", "2012-01-21      562\n", "2012-06-17      562\n", "2012-07-28      561\n", "2010-11-30      557\n", "2011-09-10      554\n", "2011-03-05      553\n", "2011-07-01      551\n", "2010-10-20      550\n", "2010-05-12      549\n", "2011-08-26      549\n", "2012-02-04      547\n", "2012-02-01      547\n", "2011-08-13      546\n", "2011-01-05      544\n", "2011-09-05      543\n", "2011-01-01      543\n", "2012-01-13      542\n", "2011-12-19      541\n", "2011-08-30      541\n", "2011-06-23      539\n", "2010-05-10      536\n", "2010-12-06      536\n", "2011-03-06      535\n", "2011-08-07      535\n", "2011-01-10      535\n", "2011-12-09      535\n", "2010-10-05      534\n", "2011-06-12      534\n", "2011-05-24      533\n", "2012-04-08      533\n", "2012-03-04      533\n", "2011-12-20      532\n", "2010-09-27      531\n", "2012-06-30      530\n", "2011-06-11      530\n", "2011-12-14      530\n", "2012-11-22      529\n", "2012-12-23      526\n", "2011-04-16      526\n", "2011-10-08      526\n", "2011-08-14      526\n", "2012-02-15      525\n", "2011-06-05      525\n", "2011-07-17      524\n", "2012-01-31      524\n", "2013-11-19      523\n", "2012-07-04      521\n", "2011-04-09      519\n", "2011-09-03      519\n", "2010-07-13      518\n", "2011-09-16      517\n", "2012-02-23      516\n", "2011-07-10      514\n", "2012-02-13      513\n", "2012-03-03      513\n", "2012-02-16      513\n", "2011-03-25      513\n", "2010-09-29      512\n", "2011-09-11      512\n", "2010-07-06      511\n", "2011-12-21      510\n", "2012-03-10      510\n", "2011-10-01      509\n", "2010-11-17      508\n", "2011-03-26      508\n", "2011-03-12      507\n", "2010-12-07      506\n", "2013-11-14      506\n", "2012-01-07      505\n", "2011-07-16      504\n", "2011-12-15      503\n", "2011-07-28      501\n", "2010-07-26      501\n", "2010-11-15      500\n", "2010-11-18      500\n", "2011-11-05      500\n", "2011-04-22      499\n", "2012-02-10      499\n", "2010-12-29      499\n", "2012-09-02      499\n", "2010-08-16      499\n", "2011-01-12      497\n", "2010-12-02      497\n", "2011-12-26      495\n", "2010-08-18      495\n", "2011-10-16      494\n", "2011-09-24      493\n", "2011-04-17      492\n", "2010-07-27      492\n", "2010-07-20      491\n", "2010-08-17      490\n", "2010-03-05      489\n", "2011-01-07      487\n", "2011-01-13      486\n", "2011-04-26      485\n", "2011-03-17      484\n", "2010-07-09      482\n", "2010-06-21      482\n", "2010-08-23      481\n", "2010-08-24      480\n", "2012-05-26      480\n", "2011-06-25      480\n", "2011-10-02      479\n", "2011-05-31      477\n", "2010-12-01      476\n", "2012-01-14      475\n", "2012-03-11      475\n", "2010-09-30      474\n", "2012-01-08      473\n", "2010-06-22      471\n", "2010-10-04      471\n", "2012-01-01      470\n", "2011-05-25      469\n", "2011-02-08      469\n", "2010-12-31      466\n", "2010-08-02      466\n", "2011-06-18      466\n", "2011-11-06      465\n", "2011-04-27      465\n", "2010-07-08      464\n", "2010-11-10      464\n", "2011-04-01      463\n", "2010-11-08      463\n", "2010-11-03      462\n", "2010-08-10      462\n", "2011-10-23      460\n", "2010-07-14      460\n", "2012-02-20      459\n", "2011-08-20      459\n", "2011-09-18      456\n", "2013-11-18      455\n", "2010-11-04      454\n", "2010-10-25      454\n", "2010-11-09      453\n", "2010-06-03      453\n", "2011-09-04      452\n", "2010-12-03      451\n", "2010-06-16      451\n", "2011-02-12      450\n", "2010-07-01      450\n", "2010-08-25      450\n", "2012-05-28      449\n", "2011-09-17      449\n", "2010-09-07      448\n", "2012-02-17      448\n", "2011-04-02      448\n", "2010-07-19      448\n", "2011-08-29      447\n", "2010-10-21      447\n", "2011-07-02      446\n", "2011-01-25      446\n", "2010-07-23      445\n", "2010-07-07      445\n", "2010-08-09      444\n", "2010-11-29      444\n", "2012-02-14      444\n", "2011-01-31      443\n", "2010-06-23      443\n", "2011-01-20      443\n", "2010-09-13      443\n", "2010-12-30      443\n", "2011-07-04      442\n", "2010-06-24      442\n", "2011-01-15      441\n", "2010-12-14      441\n", "2011-02-10      440\n", "2012-02-05      440\n", "2010-12-28      440\n", "2012-03-18      440\n", "2010-12-08      440\n", "2010-07-22      439\n", "2012-12-25      439\n", "2011-01-24      439\n", "2011-12-31      437\n", "2011-02-09      437\n", "2010-09-08      435\n", "2010-11-22      435\n", "2010-09-14      435\n", "2010-09-15      434\n", "2010-09-01      433\n", "2010-06-07      432\n", "2010-06-08      432\n", "2010-06-29      432\n", "2010-07-21      432\n", "2010-10-12      432\n", "2010-10-01      431\n", "2011-06-26      431\n", "2011-07-29      430\n", "2010-07-28      430\n", "2010-10-06      430\n", "2010-12-27      429\n", "2010-07-12      428\n", "2012-01-22      427\n", "2010-09-20      427\n", "2010-11-11      427\n", "2011-01-19      427\n", "2010-06-15      426\n", "2011-06-19      426\n", "2010-10-11      426\n", "2012-01-28      426\n", "2011-04-28      425\n", "2011-01-21      424\n", "2011-10-09      424\n", "2012-02-24      423\n", "2010-06-02      422\n", "2011-03-18      422\n", "2010-12-13      421\n", "2011-10-22      421\n", "2010-09-22      420\n", "2010-08-11      420\n", "2010-06-14      419\n", "2011-03-27      419\n", "2010-12-15      418\n", "2011-01-26      418\n", "2011-02-01      418\n", "2011-07-03      418\n", "2011-01-14      417\n", "2010-10-14      416\n", "2010-10-18      416\n", "2010-12-10      416\n", "2011-05-26      416\n", "2011-01-08      415\n", "2011-04-10      415\n", "2010-06-28      415\n", "2011-02-07      415\n", "2011-02-13      415\n", "2010-06-09      415\n", "2011-08-21      414\n", "2011-04-03      413\n", "2010-06-30      413\n", "2013-11-20      412\n", "2010-11-05      411\n", "2010-12-21      410\n", "2010-10-13      410\n", "2012-02-11      409\n", "2011-03-19      408\n", "2011-01-18      408\n", "2011-03-20      408\n", "2010-09-21      407\n", "2010-12-20      407\n", "2010-12-09      407\n", "2011-02-19      407\n", "2011-10-29      406\n", "2010-09-16      405\n", "2010-05-17      405\n", "2011-02-02      404\n", "2013-11-21      404\n", "2010-08-19      402\n", "2012-01-29      400\n", "2010-11-19      400\n", "2012-01-15      400\n", "2010-05-13      398\n", "2010-05-18      397\n", "2010-06-25      396\n", "2010-08-26      395\n", "2011-01-28      395\n", "2010-11-02      394\n", "2011-12-11      393\n", "2011-12-16      392\n", "2010-11-23      391\n", "2009-09-14      391\n", "2011-12-10      391\n", "2010-08-04      390\n", "2010-07-29      390\n", "2011-04-29      389\n", "2011-01-17      389\n", "2010-11-01      388\n", "2010-08-03      387\n", "2011-01-27      386\n", "2011-06-10      386\n", "2010-09-09      385\n", "2010-09-10      384\n", "2010-10-07      384\n", "2010-08-30      384\n", "2011-12-22      384\n", "2011-10-30      383\n", "2010-09-23      381\n", "2010-10-08      381\n", "2008-01-13      380\n", "2010-06-01      380\n", "2011-01-02      380\n", "2012-04-28      378\n", "2010-07-15      376\n", "2010-12-16      376\n", "2010-08-05      376\n", "2013-12-02      374\n", "2010-04-12      373\n", "2011-02-27      373\n", "2010-06-17      373\n", "2011-11-24      372\n", "2010-11-12      372\n", "2010-12-04      372\n", "2013-12-03      371\n", "2011-04-23      370\n", "2010-08-31      370\n", "2011-01-09      369\n", "2010-08-06      367\n", "2010-08-20      366\n", "2011-04-24      366\n", "2012-02-18      363\n", "2010-05-04      363\n", "2010-05-05      362\n", "2011-02-03      362\n", "2010-06-04      361\n", "2009-09-15      358\n", "2010-08-12      358\n", "2012-05-27      355\n", "2010-06-10      353\n", "2010-10-15      351\n", "2010-07-05      351\n", "2011-02-26      349\n", "2011-03-29      349\n", "2009-06-02      349\n", "2013-11-25      347\n", "2010-10-22      347\n", "2010-09-24      346\n", "2011-02-04      345\n", "2009-07-08      344\n", "2010-11-06      344\n", "2011-03-30      343\n", "2010-05-14      342\n", "2010-04-26      342\n", "2010-07-02      341\n", "2010-05-03      340\n", "2009-05-27      340\n", "2010-05-06      339\n", "2010-05-15      338\n", "2010-12-11      338\n", "2010-12-05      338\n", "2010-09-02      338\n", "2012-02-19      338\n", "2009-06-09      337\n", "2011-01-22      337\n", "2013-11-26      337\n", "2011-03-31      337\n", "2010-10-02      334\n", "2012-02-12      333\n", "2011-02-05      333\n", "2009-06-08      333\n", "2010-11-28      332\n", "2010-06-11      332\n", "2010-08-13      332\n", "2011-01-23      331\n", "2010-03-08      331\n", "2010-07-16      331\n", "2010-09-03      330\n", "2011-05-30      330\n", "2010-04-07      326\n", "2010-07-30      325\n", "2010-06-18      325\n", "2011-05-27      325\n", "2008-03-21      324\n", "2010-10-03      324\n", "2009-06-03      322\n", "2010-12-23      321\n", "2009-05-28      321\n", "2011-03-13      320\n", "2008-01-14      320\n", "2009-05-20      318\n", "2012-04-29      316\n", "2009-05-05      316\n", "2009-07-27      315\n", "2010-10-28      315\n", "2011-12-18      314\n", "2010-09-06      314\n", "2010-12-12      313\n", "2010-12-22      313\n", "2009-06-17      312\n", "2010-05-07      312\n", "2009-05-26      312\n", "2010-07-10      312\n", "2011-01-16      312\n", "2010-07-24      311\n", "2008-04-05      310\n", "2010-11-20      310\n", "2009-05-19      309\n", "2009-06-16      307\n", "2008-12-29      304\n", "2009-06-10      303\n", "2010-11-26      302\n", "2011-12-17      302\n", "2011-02-20      302\n", "2013-11-22      301\n", "2010-11-27      300\n", "2010-09-17      300\n", "2009-06-15      299\n", "2009-04-27      298\n", "2009-04-16      298\n", "2010-09-26      298\n", "2011-04-30      298\n", "2009-06-22      297\n", "2011-08-28      297\n", "2010-11-07      296\n", "2010-06-06      296\n", "2011-01-30      296\n", "2011-05-01      293\n", "2009-06-18      293\n", "2009-04-20      293\n", "2010-08-27      292\n", "2009-04-21      292\n", "2010-06-26      291\n", "2010-05-24      291\n", "2010-03-15      291\n", "2011-01-29      291\n", "2010-11-21      290\n", "2012-02-26      288\n", "2009-06-23      288\n", "2013-11-15      288\n", "2010-12-17      288\n", "2011-02-06      287\n", "2010-10-26      286\n", "2010-08-22      285\n", "2010-04-27      285\n", "2011-08-27      284\n", "2010-11-13      284\n", "2010-10-09      283\n", "2009-07-07      282\n", "2009-06-01      281\n", "2009-06-29      281\n", "2009-07-01      281\n", "2010-07-17      279\n", "2010-10-29      279\n", "2009-05-06      278\n", "2009-06-19      278\n", "2009-05-12      277\n", "2010-12-19      277\n", "2009-06-04      277\n", "2009-05-21      276\n", "2010-09-25      276\n", "2010-10-27      276\n", "2011-12-23      276\n", "2010-11-14      276\n", "2010-09-11      273\n", "2010-04-28      272\n", "2010-04-13      272\n", "2010-12-18      271\n", "2010-08-21      270\n", "2010-06-13      268\n", "2009-04-28      268\n", "2010-11-24      267\n", "2009-04-23      267\n", "2010-04-21      266\n", "2010-01-26      265\n", "2010-04-22      263\n", "2010-04-15      263\n", "2009-07-02      263\n", "2010-04-14      262\n", "2010-10-10      262\n", "2010-06-27      261\n", "2010-02-02      260\n", "2010-10-17      259\n", "2010-08-07      259\n", "2009-05-29      259\n", "2010-07-25      259\n", "2009-05-11      259\n", "2010-04-29      258\n", "2010-08-15      257\n", "2010-05-20      257\n", "2009-06-11      257\n", "2010-05-26      256\n", "2009-06-24      255\n", "2012-02-25      255\n", "2010-10-23      255\n", "2010-08-14      254\n", "2010-09-18      252\n", "2010-06-12      252\n", "2009-06-05      252\n", "2008-04-06      251\n", "2009-07-06      251\n", "2010-09-05      251\n", "2010-04-19      250\n", "2010-05-02      250\n", "2009-08-26      249\n", "2010-03-10      249\n", "2010-02-03      248\n", "2009-09-28      248\n", "2010-06-05      247\n", "2010-04-20      247\n", "2009-04-30      246\n", "2010-09-12      246\n", "2009-06-30      246\n", "2010-03-09      245\n", "2010-01-25      245\n", "2010-05-25      244\n", "2008-11-04      244\n", "2010-05-31      242\n", "2010-03-23      242\n", "2010-05-19      241\n", "2010-08-01      241\n", "2009-06-25      241\n", "2013-11-16      240\n", "2010-03-02      239\n", "2010-07-31      239\n", "2010-02-18      238\n", "2010-04-06      238\n", "2010-03-11      238\n", "2009-08-25      237\n", "2009-09-16      237\n", "2010-09-04      236\n", "2010-06-19      235\n", "2009-04-15      234\n", "2010-12-26      233\n", "2010-09-19      233\n", "2010-04-30      233\n", "2010-08-29      233\n", "2010-10-31      233\n", "2010-10-24      233\n", "2010-02-10      233\n", "2009-07-09      232\n", "2009-04-09      232\n", "2010-03-18      231\n", "2010-08-08      231\n", "2013-12-01      230\n", "2010-03-17      230\n", "2010-04-23      229\n", "2010-03-22      229\n", "2008-12-28      229\n", "2010-03-16      229\n", "2010-08-28      229\n", "2011-05-29      228\n", "2010-02-17      228\n", "2010-07-18      228\n", "2010-05-27      226\n", "2009-11-17      225\n", "2013-11-27      225\n", "2010-03-04      225\n", "2010-01-20      224\n", "2009-04-29      224\n", "2010-04-05      224\n", "2010-02-08      224\n", "2010-07-11      222\n", "2010-02-22      221\n", "2009-05-04      221\n", "2009-03-17      220\n", "2009-03-30      220\n", "2009-07-20      218\n", "2009-08-24      217\n", "2009-12-02      217\n", "2010-10-30      217\n", "2010-10-16      216\n", "2009-05-13      215\n", "2009-09-23      215\n", "2010-04-16      214\n", "2009-05-18      214\n", "2011-05-28      213\n", "2009-07-21      212\n", "2010-02-23      212\n", "2013-11-24      212\n", "2009-05-14      212\n", "2009-04-17      211\n", "2010-01-27      211\n", "2010-03-25      210\n", "2009-09-22      210\n", "2010-04-08      210\n", "2013-11-23      210\n", "2010-03-03      210\n", "2010-05-08      210\n", "2010-02-09      209\n", "2013-11-17      208\n", "2009-12-07      207\n", "2009-12-03      206\n", "2009-12-09      206\n", "2010-02-11      206\n", "2010-01-21      205\n", "2010-04-11      204\n", "2010-01-06      204\n", "2010-03-26      204\n", "2009-07-13      204\n", "2009-04-22      203\n", "2010-02-24      203\n", "2009-09-24      203\n", "2010-01-19      203\n", "2010-03-31      203\n", "2010-06-20      203\n", "2010-02-16      202\n", "2010-02-01      202\n", "2009-03-26      201\n", "2010-03-01      201\n", "2009-07-10      201\n", "2010-01-07      201\n", "2009-05-07      200\n", "2009-10-01      200\n", "2009-05-22      200\n", "2010-03-24      199\n", "2008-03-24      199\n", "2010-01-15      199\n", "2009-03-09      198\n", "2010-02-25      198\n", "2009-12-08      197\n", "2013-11-30      196\n", "2009-07-22      196\n", "2008-12-30      195\n", "2009-07-15      195\n", "2009-04-24      195\n", "2013-11-29      194\n", "2010-05-21      194\n", "2009-06-26      194\n", "2010-03-19      193\n", "2010-07-04      192\n", "2010-04-24      192\n", "2010-03-12      192\n", "2009-11-30      191\n", "2009-09-17      191\n", "2009-05-15      191\n", "2010-01-14      190\n", "2010-01-18      190\n", "2010-05-28      189\n", "2009-10-05      189\n", "2010-05-16      189\n", "2009-07-16      188\n", "2009-11-09      188\n", "2010-05-09      187\n", "2009-05-25      187\n", "2009-03-24      186\n", "2009-04-13      186\n", "2010-01-11      186\n", "2008-01-15      186\n", "2009-07-28      186\n", "2010-04-09      185\n", "2009-06-20      185\n", "2009-06-12      185\n", "2009-09-29      185\n", "2009-10-12      184\n", "2009-09-21      184\n", "2010-01-05      184\n", "2009-12-01      184\n", "2009-11-10      183\n", "2010-05-01      183\n", "2009-03-25      182\n", "2010-07-03      182\n", "2010-03-29      181\n", "2009-12-14      180\n", "2009-11-03      180\n", "2010-12-24      180\n", "2009-05-30      179\n", "2010-04-01      178\n", "2010-03-30      178\n", "2009-07-14      178\n", "2010-04-25      178\n", "2008-03-22      178\n", "2009-05-08      177\n", "2009-07-23      176\n", "2010-01-12      176\n", "2009-04-10      175\n", "2010-01-13      175\n", "2009-11-25      175\n", "2010-04-02      174\n", "2009-10-07      174\n", "2009-11-11      174\n", "2010-12-25      172\n", "2010-02-19      172\n", "2009-09-25      172\n", "2009-08-03      171\n", "2009-09-30      171\n", "2010-01-28      170\n", "2010-11-25      170\n", "2009-04-07      170\n", "2009-03-31      170\n", "2010-02-15      170\n", "2009-03-22      170\n", "2009-03-23      168\n", "2009-11-18      168\n", "2009-10-02      167\n", "2009-10-06      167\n", "2008-10-20      167\n", "2009-07-31      167\n", "2009-10-09      167\n", "2009-10-14      166\n", "2009-10-13      164\n", "2009-04-06      164\n", "2009-12-28      163\n", "2009-04-08      162\n", "2010-03-07      162\n", "2009-07-29      162\n", "2009-12-15      161\n", "2009-04-14      160\n", "2009-05-23      160\n", "2009-08-31      160\n", "2009-08-17      160\n", "2009-03-19      160\n", "2009-08-10      160\n", "2009-12-04      159\n", "2009-11-12      159\n", "2009-12-29      159\n", "2010-01-08      159\n", "2008-04-08      159\n", "2009-06-14      159\n", "2009-06-07      159\n", "2009-09-08      158\n", "2009-09-02      158\n", "2008-11-12      158\n", "2009-04-18      158\n", "2009-04-01      158\n", "2009-09-09      158\n", "2009-03-20      158\n", "2010-04-10      158\n", "2011-12-24      157\n", "2009-03-18      157\n", "2009-03-10      156\n", "2009-06-21      156\n", "2009-03-27      156\n", "2009-08-27      156\n", "2009-12-11      155\n", "2009-10-19      155\n", "2010-02-05      154\n", "2008-11-05      154\n", "2009-09-18      153\n", "2009-06-13      153\n", "2009-05-31      153\n", "2009-03-21      152\n", "2009-12-10      152\n", "2010-01-04      152\n", "2009-12-18      151\n", "2009-08-05      151\n", "2010-02-04      151\n", "2009-06-28      150\n", "2009-10-28      150\n", "2010-03-06      150\n", "2009-11-02      150\n", "2009-11-04      150\n", "2009-03-04      150\n", "2009-09-13      149\n", "2009-05-01      149\n", "2009-08-18      149\n", "2009-11-16      148\n", "2009-06-06      148\n", "2009-10-22      148\n", "2009-09-12      148\n", "2010-05-22      148\n", "2010-03-13      148\n", "2010-05-23      148\n", "2009-09-11      148\n", "2009-08-06      147\n", "2009-08-04      147\n", "2009-08-13      146\n", "2009-01-05      146\n", "2009-10-27      146\n", "2008-03-25      146\n", "2008-01-16      145\n", "2010-02-26      145\n", "2009-11-23      145\n", "2010-02-12      145\n", "2009-05-09      144\n", "2009-04-02      143\n", "2009-02-24      143\n", "2009-04-26      143\n", "2009-07-30      143\n", "2009-10-20      143\n", "2008-11-06      142\n", "2009-07-03      142\n", "2009-11-24      142\n", "2009-08-12      142\n", "2009-08-11      142\n", "2009-10-08      142\n", "2009-08-20      141\n", "2009-05-24      140\n", "2009-04-19      140\n", "2010-03-28      139\n", "2008-11-10      139\n", "2008-10-21      139\n", "2008-11-17      139\n", "2009-01-26      139\n", "2009-02-04      138\n", "2009-04-03      137\n", "2009-09-01      135\n", "2009-08-28      135\n", "2009-03-28      135\n", "2008-12-09      135\n", "2009-03-29      134\n", "2010-03-27      134\n", "2009-11-19      134\n", "2009-09-03      134\n", "2008-11-19      134\n", "2010-01-22      133\n", "2010-03-20      133\n", "2009-12-30      133\n", "2009-09-04      133\n", "2011-12-25      133\n", "2008-03-26      133\n", "2008-11-18      132\n", "2009-10-16      132\n", "2009-02-02      132\n", "2009-03-16      132\n", "2010-02-20      132\n", "2010-05-29      132\n", "2009-09-20      132\n", "2008-11-11      131\n", "2009-10-26      131\n", "2009-07-17      131\n", "2009-12-22      131\n", "2009-09-10      130\n", "2010-01-30      130\n", "2009-05-10      129\n", "2009-11-05      129\n", "2009-10-15      129\n", "2008-11-14      129\n", "2009-08-14      129\n", "2010-02-06      129\n", "2008-12-11      129\n", "2009-04-25      128\n", "2009-02-05      127\n", "2009-12-16      127\n", "2009-01-27      127\n", "2009-01-13      127\n", "2008-11-24      127\n", "2009-06-27      126\n", "2009-05-03      126\n", "2008-02-11      126\n", "2009-12-17      126\n", "2008-10-30      125\n", "2008-03-13      125\n", "2010-01-31      125\n", "2010-05-30      125\n", "2010-04-17      125\n", "2009-11-13      125\n", "2009-09-27      125\n", "2009-12-21      125\n", "2009-08-19      124\n", "2009-11-08      124\n", "2008-12-01      123\n", "2009-01-06      123\n", "2009-07-24      123\n", "2009-03-11      123\n", "2008-03-23      122\n", "2009-01-14      122\n", "2009-05-02      122\n", "2009-10-04      122\n", "2009-03-05      121\n", "2009-03-12      121\n", "2010-03-14      121\n", "2008-12-16      120\n", "2010-01-29      120\n", "2010-01-16      118\n", "2008-12-10      118\n", "2008-12-26      118\n", "2009-01-28      117\n", "2008-08-26      117\n", "2011-05-17      117\n", "2009-08-21      117\n", "2009-07-11      117\n", "2008-12-17      117\n", "2009-07-12      117\n", "2009-08-07      117\n", "2009-02-18      116\n", "2008-11-07      116\n", "2009-10-23      116\n", "2010-01-10      116\n", "2010-02-21      116\n", "2009-02-25      115\n", "2009-12-06      115\n", "2009-05-16      115\n", "2008-04-09      115\n", "2009-11-27      114\n", "2009-02-23      114\n", "2009-04-11      114\n", "2010-03-21      114\n", "2013-11-28      114\n", "2008-10-29      113\n", "2009-01-15      113\n", "2011-05-16      113\n", "2008-11-16      113\n", "2008-12-18      113\n", "2008-11-13      113\n", "2010-01-17      113\n", "2009-10-21      112\n", "2009-10-29      112\n", "2009-03-02      111\n", "2008-10-17      111\n", "2008-10-15      111\n", "2008-12-08      111\n", "2009-02-19      110\n", "2009-02-03      110\n", "2009-01-07      110\n", "2009-01-29      109\n", "2008-11-25      109\n", "2009-02-09      109\n", "2008-11-03      109\n", "2008-11-20      109\n", "2009-08-22      109\n", "2009-07-19      109\n", "2010-04-18      108\n", "2009-05-17      108\n", "2009-02-17      108\n", "2010-01-23      108\n", "2009-03-13      108\n", "2009-04-12      107\n", "2009-01-21      107\n", "2009-12-13      107\n", "2009-01-08      107\n", "2008-10-16      106\n", "2008-10-22      106\n", "2009-09-26      106\n", "2009-01-22      106\n", "2010-02-28      106\n", "2008-12-05      105\n", "2009-03-03      105\n", "2009-11-20      105\n", "2009-04-05      105\n", "2007-11-28      105\n", "2010-04-03      104\n", "2009-03-14      104\n", "2008-03-10      104\n", "2009-10-03      103\n", "2008-12-15      103\n", "2009-09-05      103\n", "2008-10-23      103\n", "2009-01-12      103\n", "2009-10-11      103\n", "2009-10-10      102\n", "2008-11-21      102\n", "2009-08-30      102\n", "2010-02-07      101\n", "2008-01-28      101\n", "2008-03-19      101\n", "2010-01-09      101\n", "2008-12-03      101\n", "2008-01-17      101\n", "2009-01-25      101\n", "2009-11-06      101\n", "2009-02-16      100\n", "2009-11-28      100\n", "2009-09-07      100\n", "2008-10-26      100\n", "2009-01-18      100\n", "2008-12-04       99\n", "2010-01-24       99\n", "2009-10-30       99\n", "2009-01-04       98\n", "2009-07-05       98\n", "2010-02-14       98\n", "2009-11-01       97\n", "2009-03-15       97\n", "2009-12-05       97\n", "2009-09-19       97\n", "2008-03-20       97\n", "2009-08-23       97\n", "2009-02-12       97\n", "2008-10-28       97\n", "2009-02-10       96\n", "2009-12-23       96\n", "2008-12-02       96\n", "2009-01-23       95\n", "2011-05-11       95\n", "2008-03-18       95\n", "2008-02-25       95\n", "2008-12-22       94\n", "2009-07-18       94\n", "2009-02-26       94\n", "2007-12-27       94\n", "2009-08-01       93\n", "2011-05-10       93\n", "2009-01-09       93\n", "2011-05-18       93\n", "2009-02-27       93\n", "2009-08-02       92\n", "2011-05-04       92\n", "2009-02-06       92\n", "2011-05-20       92\n", "2011-05-09       91\n", "2009-08-29       91\n", "2009-02-20       91\n", "2009-02-08       90\n", "2009-01-17       90\n", "2009-01-19       90\n", "2009-09-06       90\n", "2009-11-14       90\n", "2008-11-08       90\n", "2009-11-29       90\n", "2009-04-04       89\n", "2009-01-20       89\n", "2010-02-13       89\n", "2009-07-04       89\n", "2009-02-11       88\n", "2009-10-24       88\n", "2008-11-09       88\n", "2008-03-05       88\n", "2009-01-02       88\n", "2009-12-31       87\n", "2008-02-12       87\n", "2010-02-27       87\n", "2008-11-22       86\n", "2009-11-22       86\n", "2008-10-31       86\n", "2008-02-05       86\n", "2009-12-26       86\n", "2008-12-12       86\n", "2009-12-19       86\n", "2011-05-06       85\n", "2009-11-21       85\n", "2008-03-12       85\n", "2008-03-11       85\n", "2008-10-27       85\n", "2010-04-04       85\n", "2008-03-17       85\n", "2009-08-09       85\n", "2010-01-02       85\n", "2008-03-04       85\n", "2008-02-26       85\n", "2007-12-04       84\n", "2009-01-30       84\n", "2009-01-03       84\n", "2008-11-23       84\n", "2011-05-13       83\n", "2008-01-03       83\n", "2008-11-26       83\n", "2011-05-12       83\n", "2008-10-18       83\n", "2008-04-02       83\n", "2009-01-24       83\n", "2007-12-13       82\n", "2009-07-25       82\n", "2008-04-10       82\n", "2011-05-19       82\n", "2008-01-30       82\n", "2008-10-25       82\n", "2009-03-06       82\n", "2008-03-03       81\n", "2008-08-25       81\n", "2011-05-03       80\n", "2009-12-12       80\n", "2007-12-03       80\n", "2009-03-01       80\n", "2009-10-31       80\n", "2009-01-11       80\n", "2008-04-15       80\n", "2008-12-06       80\n", "2008-11-15       80\n", "2008-01-18       79\n", "2009-08-16       79\n", "2008-10-24       79\n", "2008-01-21       79\n", "2008-12-19       79\n", "2008-01-07       78\n", "2008-11-02       78\n", "2008-01-29       78\n", "2009-08-08       78\n", "2009-01-10       78\n", "2009-08-15       78\n", "2009-12-20       77\n", "2008-03-07       77\n", "2007-12-17       77\n", "2008-03-14       77\n", "2008-01-02       77\n", "2008-02-06       77\n", "2008-02-20       77\n", "2007-11-30       76\n", "2007-11-29       76\n", "2007-12-18       76\n", "2009-11-15       76\n", "2009-01-16       76\n", "2008-10-14       76\n", "2008-03-15       76\n", "2008-02-04       76\n", "2009-10-18       75\n", "2009-07-26       75\n", "2009-10-17       75\n", "2008-01-20       75\n", "2008-01-04       75\n", "2008-03-28       75\n", "2008-01-19       75\n", "2008-10-19       75\n", "2008-02-13       75\n", "2009-11-07       74\n", "2008-11-01       74\n", "2009-12-24       74\n", "2008-12-07       74\n", "2008-01-23       74\n", "2007-12-06       74\n", "2009-10-25       73\n", "2007-12-12       73\n", "2008-06-24       73\n", "2008-04-28       73\n", "2010-01-03       72\n", "2009-02-07       72\n", "2008-12-20       72\n", "2008-12-14       72\n", "2008-03-02       72\n", "2007-11-26       72\n", "2008-03-09       71\n", "2007-11-23       71\n", "2008-06-09       70\n", "2007-11-27       70\n", "2008-03-27       70\n", "2008-01-12       70\n", "2008-02-08       69\n", "2008-02-19       69\n", "2009-02-21       69\n", "2008-11-28       69\n", "2007-12-10       69\n", "2009-02-13       69\n", "2007-12-05       69\n", "2009-02-28       68\n", "2008-06-20       68\n", "2008-02-15       68\n", "2008-01-22       68\n", "2008-12-23       68\n", "2008-02-10       68\n", "2008-03-06       67\n", "2008-11-29       67\n", "2008-02-28       67\n", "2011-05-05       67\n", "2008-01-09       66\n", "2008-01-24       66\n", "2008-02-07       66\n", "2008-04-24       66\n", "2007-12-28       66\n", "2008-01-08       65\n", "2009-12-27       65\n", "2008-04-29       65\n", "2008-01-31       64\n", "2008-02-21       64\n", "2008-04-23       64\n", "2008-07-15       64\n", "2008-02-18       63\n", "2009-03-08       63\n", "2008-01-10       63\n", "2007-12-26       63\n", "2009-03-07       62\n", "2008-09-10       62\n", "2008-06-16       62\n", "2008-03-16       62\n", "2007-12-07       62\n", "2007-12-02       62\n", "2007-12-14       61\n", "2008-04-01       61\n", "2008-02-17       61\n", "2009-01-31       61\n", "2007-11-25       61\n", "2008-05-06       61\n", "2008-07-30       60\n", "2008-03-31       60\n", "2008-07-11       60\n", "2008-07-17       60\n", "2007-11-02       60\n", "2008-01-11       59\n", "2011-05-14       59\n", "2008-02-27       59\n", "2008-12-24       59\n", "2008-02-14       59\n", "2009-02-01       58\n", "2008-08-06       58\n", "2008-04-16       58\n", "2008-12-21       58\n", "2008-07-10       57\n", "2009-11-26       57\n", "2008-12-13       57\n", "2008-02-09       57\n", "2011-05-15       57\n", "2007-10-22       56\n", "2008-02-03       56\n", "2009-02-15       56\n", "2008-08-27       56\n", "2009-02-22       56\n", "2008-06-26       56\n", "2008-11-30       56\n", "2008-06-25       56\n", "2008-06-23       55\n", "2008-06-19       55\n", "2008-04-11       55\n", "2008-04-22       55\n", "2008-04-27       54\n", "2007-12-29       54\n", "2008-06-10       54\n", "2010-01-01       54\n", "2008-05-01       54\n", "2008-07-21       54\n", "2008-04-03       54\n", "2008-09-09       54\n", "2008-01-25       54\n", "2008-08-07       54\n", "2008-04-21       54\n", "2008-07-09       54\n", "2008-06-04       54\n", "2008-09-29       53\n", "2008-06-03       53\n", "2008-02-29       53\n", "2008-05-05       52\n", "2008-02-01       52\n", "2007-12-11       52\n", "2008-07-14       52\n", "2008-06-17       51\n", "2008-10-07       51\n", "2008-10-13       51\n", "2008-03-30       51\n", "2008-04-25       51\n", "2008-04-04       51\n", "2009-01-01       50\n", "2008-08-04       50\n", "2008-03-08       50\n", "2008-06-18       50\n", "2007-10-15       50\n", "2008-03-29       50\n", "2008-07-28       49\n", "2008-04-14       49\n", "2008-04-17       49\n", "2007-10-17       49\n", "2008-12-31       49\n", "2008-12-27       49\n", "2008-04-13       49\n", "2007-12-09       49\n", "2008-05-22       48\n", "2008-08-21       48\n", "2008-02-16       48\n", "2008-05-14       48\n", "2007-11-24       48\n", "2008-05-20       48\n", "2008-07-31       48\n", "2008-08-05       48\n", "2008-07-24       47\n", "2008-09-16       47\n", "2008-01-27       47\n", "2008-05-02       47\n", "2008-07-13       47\n", "2008-01-26       47\n", "2007-12-30       47\n", "2008-03-01       46\n", "2008-05-28       46\n", "2009-02-14       46\n", "2007-12-20       46\n", "2007-12-16       46\n", "2008-06-05       46\n", "2008-02-02       45\n", "2008-08-22       45\n", "2007-12-19       45\n", "2007-11-13       44\n", "2009-12-25       44\n", "2008-08-01       44\n", "2008-05-11       44\n", "2008-02-22       44\n", "2008-05-29       44\n", "2008-06-12       44\n", "2008-07-08       44\n", "2008-08-20       44\n", "2008-08-12       44\n", "2008-04-20       44\n", "2011-05-07       43\n", "2008-08-08       43\n", "2008-04-30       43\n", "2007-10-16       43\n", "2008-06-21       43\n", "2008-09-23       43\n", "2008-10-03       43\n", "2011-05-08       43\n", "2008-05-07       42\n", "2008-09-19       42\n", "2008-06-02       42\n", "2008-04-18       42\n", "2008-07-29       42\n", "2008-05-30       42\n", "2007-11-19       42\n", "2008-07-22       42\n", "2008-05-27       42\n", "2007-11-08       42\n", "2008-08-24       42\n", "2008-06-22       41\n", "2007-10-09       41\n", "2007-10-02       41\n", "2008-06-27       41\n", "2008-06-13       41\n", "2008-10-08       41\n", "2008-01-05       41\n", "2008-08-13       40\n", "2008-07-18       40\n", "2008-05-21       40\n", "2008-11-27       40\n", "2007-12-01       40\n", "2008-01-01       40\n", "2008-04-26       40\n", "2008-09-18       39\n", "2008-02-24       39\n", "2008-08-11       39\n", "2008-05-19       39\n", "2011-05-22       39\n", "2008-09-15       39\n", "2007-12-15       39\n", "2008-08-15       39\n", "2007-11-20       39\n", "2008-06-11       39\n", "2008-09-22       38\n", "2007-11-04       38\n", "2007-10-26       38\n", "2007-12-31       38\n", "2008-10-06       38\n", "2008-01-06       38\n", "2008-09-24       38\n", "2008-05-23       38\n", "2008-05-12       38\n", "2007-10-25       37\n", "2008-06-30       37\n", "2008-05-13       37\n", "2008-07-12       37\n", "2008-04-12       37\n", "2008-05-31       37\n", "2007-11-07       36\n", "2007-10-29       36\n", "2007-12-21       36\n", "2007-11-21       36\n", "2008-09-12       36\n", "2008-09-04       36\n", "2008-06-06       36\n", "2008-05-26       36\n", "2007-10-24       36\n", "2008-10-09       36\n", "2008-10-02       36\n", "2007-11-05       36\n", "2007-11-01       36\n", "2007-11-14       35\n", "2008-05-08       35\n", "2008-07-23       35\n", "2007-10-23       35\n", "2008-02-23       35\n", "2008-07-16       34\n", "2008-07-20       34\n", "2008-05-18       34\n", "2011-05-21       34\n", "2007-10-11       33\n", "2008-08-18       33\n", "2008-08-23       33\n", "2008-07-07       33\n", "2008-05-15       33\n", "2007-12-08       33\n", "2008-07-02       33\n", "2008-09-30       33\n", "2008-10-12       33\n", "2008-07-03       33\n", "2008-05-03       33\n", "2007-10-05       33\n", "2008-10-10       33\n", "2007-11-16       33\n", "2007-10-10       33\n", "2007-11-12       33\n", "2008-10-05       32\n", "2008-10-01       32\n", "2007-11-03       32\n", "2008-09-02       32\n", "2008-09-17       32\n", "2007-11-22       32\n", "2007-11-15       31\n", "2007-11-09       31\n", "2008-07-25       31\n", "2007-11-06       31\n", "2007-10-13       31\n", "2008-09-28       31\n", "2008-09-25       31\n", "2007-10-18       31\n", "2008-04-19       31\n", "2008-09-08       31\n", "2008-05-04       31\n", "2008-08-28       30\n", "2008-06-29       30\n", "2007-11-10       30\n", "2007-10-12       30\n", "2008-07-01       30\n", "2008-09-03       30\n", "2008-06-01       29\n", "2008-05-16       29\n", "2007-10-03       29\n", "2007-11-18       29\n", "2007-10-08       29\n", "2008-09-01       29\n", "2007-10-21       29\n", "2008-05-10       29\n", "2008-07-27       28\n", "2008-09-05       28\n", "2008-08-09       28\n", "2008-08-29       28\n", "2007-10-04       28\n", "2008-08-10       28\n", "2008-09-11       28\n", "2007-12-22       27\n", "2008-08-17       27\n", "2008-08-14       27\n", "2008-07-06       27\n", "2008-08-19       27\n", "2007-10-01       27\n", "2008-08-30       27\n", "2007-09-26       27\n", "2008-06-08       27\n", "2007-09-29       26\n", "2008-05-25       26\n", "2008-09-13       26\n", "2007-12-24       26\n", "2008-06-07       26\n", "2007-10-19       25\n", "2008-09-26       25\n", "2008-12-25       25\n", "2007-10-30       24\n", "2008-08-02       24\n", "2008-09-14       24\n", "2007-10-06       24\n", "2007-09-13       24\n", "2008-06-15       24\n", "2007-09-27       24\n", "2007-09-25       24\n", "2008-08-31       24\n", "2007-10-14       24\n", "2007-06-25       23\n", "2008-09-07       23\n", "2008-10-04       23\n", "2008-06-14       23\n", "2008-07-19       23\n", "2008-07-05       23\n", "2007-09-30       23\n", "2008-05-17       23\n", "2007-10-27       23\n", "2007-09-24       22\n", "2007-09-14       22\n", "2007-11-17       22\n", "2008-09-06       21\n", "2008-05-24       21\n", "2008-09-21       21\n", "2007-09-23       20\n", "2007-10-31       20\n", "2007-09-28       20\n", "2008-07-04       20\n", "2007-06-24       19\n", "2008-06-28       19\n", "2008-05-09       19\n", "2008-08-03       19\n", "2008-09-20       18\n", "2008-10-11       18\n", "2007-11-11       18\n", "2008-08-16       18\n", "2007-12-25       17\n", "2007-10-20       17\n", "2007-09-22       17\n", "2007-09-19       17\n", "2007-08-28       17\n", "2008-09-27       16\n", "2008-07-26       16\n", "2007-06-01       16\n", "2007-09-16       16\n", "2007-10-28       16\n", "2007-09-21       16\n", "2007-12-23       16\n", "2007-06-19       15\n", "2007-07-07       15\n", "2007-09-18       15\n", "2007-10-07       14\n", "2007-07-31       14\n", "2007-07-01       14\n", "2007-06-04       14\n", "2007-06-21       13\n", "2007-07-25       13\n", "2007-09-17       13\n", "2007-07-18       13\n", "2007-07-04       12\n", "2007-06-08       12\n", "2007-09-20       12\n", "2007-08-14       12\n", "2007-06-30       12\n", "2007-07-10       12\n", "2007-05-29       12\n", "2007-07-02       11\n", "2007-07-12       11\n", "2007-06-12       11\n", "2007-08-12       11\n", "2007-05-30       11\n", "2007-06-22       10\n", "2007-07-23       10\n", "2007-07-08       10\n", "2007-06-15       10\n", "2007-08-13       10\n", "2007-05-31       10\n", "2007-06-11       10\n", "2007-09-07       10\n", "2007-06-02       10\n", "2007-06-06       10\n", "2007-08-23       10\n", "2007-08-01        9\n", "2007-07-14        9\n", "2007-06-23        9\n", "2007-09-08        9\n", "2007-07-03        9\n", "2007-08-21        9\n", "2007-07-27        9\n", "2007-08-02        9\n", "2007-08-04        9\n", "2007-06-17        9\n", "2007-06-26        9\n", "2007-09-12        9\n", "2007-06-18        9\n", "2007-07-19        8\n", "2007-07-11        8\n", "2007-09-04        8\n", "2007-09-06        8\n", "2007-08-09        8\n", "2007-07-06        8\n", "2007-08-17        8\n", "2007-07-05        8\n", "2007-07-22        8\n", "2007-06-05        8\n", "2007-08-07        8\n", "2007-06-09        8\n", "2007-08-24        8\n", "2007-05-28        8\n", "2007-08-16        8\n", "2007-09-15        7\n", "2007-07-09        7\n", "2007-08-27        7\n", "2007-07-13        7\n", "2007-06-27        7\n", "2007-07-20        7\n", "2007-07-24        7\n", "2007-07-30        7\n", "2007-06-14        7\n", "2007-08-11        7\n", "2007-06-29        7\n", "2007-06-03        6\n", "2007-06-20        6\n", "2007-06-10        6\n", "2007-08-15        6\n", "2007-07-16        6\n", "2007-07-17        6\n", "2007-08-31        6\n", "2007-08-29        6\n", "2007-08-22        6\n", "2007-05-27        6\n", "2007-08-08        6\n", "2007-06-28        5\n", "2007-09-05        5\n", "2007-09-01        5\n", "2007-08-30        5\n", "2007-08-25        5\n", "2007-08-18        5\n", "2007-08-10        5\n", "2007-06-13        5\n", "2007-09-10        5\n", "2007-08-05        5\n", "2007-09-11        5\n", "2007-07-21        5\n", "2007-08-06        4\n", "2007-08-20        4\n", "2007-08-26        4\n", "2007-07-26        4\n", "2007-09-02        4\n", "2007-08-03        3\n", "2007-07-29        3\n", "2007-07-15        3\n", "2007-09-03        3\n", "2007-09-09        2\n", "2007-08-19        2\n", "2007-06-16        2\n", "2007-07-28        2\n", "2007-05-26        2\n", "2007-06-07        1\n", "Name: count, dtype: int64\n", "==============================\n", "Loan Title\n", "['Wedding Covered but No Honeymoon' 'Consolidating Debt'\n", " 'Want to consolidate my debt' ... 'dougie03' 'freeup'\n", " 'Business Advertising Loan']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub data rate exceeded.\n", "The Jupyter server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--ServerApp.iopub_data_rate_limit`.\n", "\n", "Current values:\n", "ServerApp.iopub_data_rate_limit=1000000.0 (bytes/sec)\n", "ServerApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[693. 703. 715. 698. 509. 645. 700. 694. 573. 710. 680. 688. 704. 708.\n", " 685. 712. 684. 686. 719. 602. 475. 695. 671. 638. 429. 632. 579. 474.\n", " 463. 593. 578. 536. 524. 520.   0. 658. 492. 581. 543. 660. 503. 556.\n", " 480. 592. 600. 460. 654. 486. 547. 454. 665. 561. 565. 479. 444. 549.\n", " 599. 650. 607. 419. 758. 504. 711. 530. 473. 606. 502. 497. 709. 412.\n", " 506. 472. 507. 567. 551. 552. 643. 647. 531. 612. 656. 528. 558. 461.\n", " 522. 690. 559. 633. 481. 679. 494. 582. 583. 468. 576. 696. 545. 626.\n", " 640. 667. 609. 450.  nan 716. 653. 621. 629. 615. 608. 628. 585. 493.\n", " 512. 598. 490. 588. 523. 557. 548. 489. 535. 639. 478. 736. 533. 635.\n", " 631. 445. 590. 619. 611. 652. 537. 525. 560. 470. 634. 603. 532. 569.\n", " 605. 544. 516. 483. 651. 604. 455. 594. 642. 529. 617. 589. 683. 508.\n", " 564. 572. 482. 515. 789. 595. 596. 646. 702. 538. 462. 625. 666. 570.\n", " 657. 655. 495. 488. 484. 510. 505. 566. 601. 534. 571. 623. 527. 701.\n", " 574. 542. 487. 433. 432. 514. 464. 554. 501. 485. 449. 726. 661. 727.\n", " 586. 563. 575. 491. 456. 597. 720. 724. 676. 562. 499. 546. 644. 539.\n", " 467. 453. 500. 519. 641. 424. 430. 469. 669. 418. 526. 618. 541. 553.\n", " 620. 440. 477. 637. 496. 465. 435. 610. 451. 471. 697. 448. 550. 428.\n", " 417. 511. 420. 577. 555. 591. 452. 614. 457. 400. 518. 662. 668. 689.\n", " 663. 622. 437. 517. 568. 705. 425. 466. 636. 513. 674. 427. 476. 664.\n", " 624. 648. 431. 627. 616. 441. 458. 659. 442. 670. 410. 613. 682. 498.\n", " 434. 580. 415. 630. 587. 540. 389. 584. 673. 717. 769. 706. 447. 407.\n", " 707. 391. 459. 436. 681. 438. 390. 746. 678. 422. 396. 750. 729. 763.\n", " 363. 649. 395. 414. 388. 699. 443. 446. 406. 521. 423. 734. 744. 786.\n", " 713. 807. 416. 741. 691. 751. 692. 739. 386. 675. 413. 723. 718. 687.\n", " 409. 761. 677. 401. 439. 730. 672. 426. 743. 380. 745. 794. 408. 738.\n", " 747. 722. 411. 421. 737. 759. 404. 725. 764. 399. 742. 765. 776. 405.\n", " 721. 733. 772. 735. 768. 385. 770. 803. 714. 775. 755. 808. 811. 731.\n", " 762. 753. 788. 740. 732. 752. 792. 402. 800. 749. 773. 797. 782. 372.\n", " 403. 377. 766. 728. 757. 787. 810. 760. 780. 754. 382. 381. 375. 756.\n", " 802. 801. 774. 777. 394. 771. 815. 748. 804. 798. 783. 368. 398. 781.\n", " 392. 397. 793. 791. 795. 778. 767. 373. 784. 790. 393. 805. 383. 821.\n", " 384. 378. 812. 809. 779. 819. 785. 806. 365. 796. 816. 813. 814. 799.\n", " 828. 818. 387. 822. 366. 374. 817. 371. 824. 820. 364. 367. 825. 823.\n", " 379. 832. 849. 826. 829. 830. 834. 836. 840. 838. 833. 839. 846. 831.\n", " 827. 843. 842. 850. 835. 841. 844. 837. 847. 848. 853. 348. 889. 878.\n", " 852. 854. 858. 883. 851. 870. 926. 891. 863. 874. 326. 866. 882. 867.\n", " 857. 860. 879. 865. 916. 869. 875. 323. 912. 904. 887. 884. 914. 873.\n", " 845. 855. 886. 907. 856. 900. 910. 922. 925. 871. 902. 864. 911. 972.\n", " 920. 868. 861. 936. 975. 881. 947. 896. 895. 955. 918. 885. 877. 862.\n", " 956. 859. 876. 880. 934. 917. 901. 921. 345. 894. 990. 905. 919. 940.\n", " 908. 963. 903. 909. 376. 872. 968. 347. 930. 888. 942. 352. 350. 890.\n", " 899. 356. 937. 927. 923. 981. 332. 952. 939. 974. 944. 967. 932. 946.\n", " 906. 361. 931. 915. 300. 354. 965. 929. 943. 353. 958. 898. 893. 945.\n", " 978. 924. 333. 360. 960. 892. 933. 897. 913. 941. 928. 362. 327. 987.\n", " 982. 957. 338. 357. 964. 949. 308. 340. 312. 343. 346. 971. 977. 339.\n", " 966. 973. 325. 306. 969. 334. 301. 314. 331. 954. 989. 322. 370. 984.\n", " 349. 935. 369. 976. 938. 951. 315. 948. 970. 979. 344. 959. 962. 961.\n", " 310. 305. 980. 342. 355. 359. 358. 953. 337. 985. 950. 324. 988. 986.\n", " 320. 307. 316. 318. 351. 328. 335. 309. 336. 313. 321. 303. 329. 341.\n", " 304. 317. 983. 330. 311. 302. 319.]\n", "Risk_Score\n", "501.0    178456\n", "643.0     89033\n", "0.0       86754\n", "620.0     82840\n", "652.0     75531\n", "651.0     69972\n", "573.0     66770\n", "653.0     64302\n", "650.0     63398\n", "647.0     62594\n", "658.0     62002\n", "649.0     61909\n", "659.0     61867\n", "655.0     61311\n", "656.0     61285\n", "657.0     61161\n", "648.0     61075\n", "646.0     60943\n", "654.0     60656\n", "663.0     60521\n", "635.0     60439\n", "645.0     60120\n", "644.0     59895\n", "681.0     59666\n", "660.0     59610\n", "640.0     59403\n", "639.0     59383\n", "638.0     58795\n", "637.0     58728\n", "642.0     58621\n", "641.0     58620\n", "661.0     58307\n", "636.0     57962\n", "631.0     57614\n", "634.0     57558\n", "630.0     57349\n", "666.0     57279\n", "633.0     57272\n", "662.0     57072\n", "664.0     56783\n", "632.0     56508\n", "629.0     56247\n", "667.0     56018\n", "665.0     55968\n", "671.0     55804\n", "628.0     55468\n", "668.0     55373\n", "627.0     55279\n", "670.0     54916\n", "669.0     54575\n", "625.0     54491\n", "626.0     54438\n", "617.0     54201\n", "624.0     54164\n", "623.0     53272\n", "672.0     52971\n", "619.0     52939\n", "622.0     52883\n", "621.0     52733\n", "674.0     52626\n", "618.0     52404\n", "673.0     52222\n", "616.0     51221\n", "675.0     51206\n", "615.0     50620\n", "676.0     50453\n", "613.0     49933\n", "614.0     49218\n", "610.0     49195\n", "677.0     49121\n", "612.0     48741\n", "611.0     48738\n", "678.0     48393\n", "680.0     47713\n", "679.0     47465\n", "608.0     47247\n", "609.0     47001\n", "607.0     46525\n", "683.0     45830\n", "605.0     45821\n", "682.0     45788\n", "606.0     45401\n", "604.0     44763\n", "599.0     44183\n", "603.0     43962\n", "602.0     43773\n", "684.0     43718\n", "601.0     43343\n", "600.0     43235\n", "685.0     42707\n", "563.0     42344\n", "686.0     42287\n", "598.0     41866\n", "689.0     41350\n", "597.0     41337\n", "687.0     40999\n", "596.0     40636\n", "595.0     40052\n", "688.0     40047\n", "594.0     39264\n", "593.0     39129\n", "592.0     39048\n", "690.0     38601\n", "691.0     38185\n", "591.0     37679\n", "590.0     37585\n", "524.0     36878\n", "692.0     36412\n", "589.0     36280\n", "588.0     35732\n", "693.0     35659\n", "586.0     35543\n", "587.0     35161\n", "694.0     34639\n", "585.0     34441\n", "695.0     34281\n", "584.0     33794\n", "581.0     33435\n", "583.0     33357\n", "696.0     33340\n", "699.0     32646\n", "697.0     32617\n", "582.0     32522\n", "580.0     32410\n", "698.0     32124\n", "579.0     31217\n", "700.0     31043\n", "552.0     30947\n", "578.0     30932\n", "577.0     30255\n", "576.0     30183\n", "701.0     29760\n", "575.0     29720\n", "702.0     29254\n", "703.0     28973\n", "574.0     28837\n", "704.0     28300\n", "708.0     27789\n", "705.0     27285\n", "706.0     26550\n", "572.0     26211\n", "544.0     26111\n", "570.0     25895\n", "707.0     25804\n", "571.0     25757\n", "567.0     25584\n", "569.0     25337\n", "568.0     25012\n", "709.0     24584\n", "517.0     24468\n", "565.0     24438\n", "566.0     24421\n", "564.0     23884\n", "710.0     23819\n", "711.0     23701\n", "712.0     23187\n", "713.0     22580\n", "560.0     22412\n", "562.0     22406\n", "714.0     22087\n", "561.0     21970\n", "559.0     21658\n", "558.0     21652\n", "715.0     21596\n", "716.0     21551\n", "557.0     21472\n", "556.0     21192\n", "555.0     21017\n", "554.0     20778\n", "717.0     20574\n", "553.0     20520\n", "718.0     20289\n", "550.0     20134\n", "719.0     20004\n", "551.0     19896\n", "549.0     19502\n", "545.0     19387\n", "720.0     19334\n", "548.0     19214\n", "547.0     19133\n", "721.0     19070\n", "546.0     18656\n", "733.0     18618\n", "722.0     18315\n", "724.0     18256\n", "512.0     18166\n", "723.0     18084\n", "543.0     17593\n", "726.0     17273\n", "542.0     17269\n", "541.0     16960\n", "540.0     16823\n", "725.0     16779\n", "536.0     16664\n", "539.0     16302\n", "727.0     16249\n", "538.0     16008\n", "728.0     15915\n", "537.0     15887\n", "535.0     15477\n", "729.0     15442\n", "508.0     15162\n", "534.0     15151\n", "533.0     15105\n", "730.0     15058\n", "532.0     14803\n", "749.0     14775\n", "731.0     14741\n", "732.0     14662\n", "530.0     14540\n", "531.0     14398\n", "529.0     14072\n", "734.0     14013\n", "526.0     13684\n", "528.0     13665\n", "527.0     13583\n", "735.0     13570\n", "525.0     13421\n", "736.0     12972\n", "737.0     12856\n", "523.0     12697\n", "522.0     12525\n", "738.0     12435\n", "521.0     12202\n", "520.0     12183\n", "739.0     12078\n", "519.0     11888\n", "741.0     11755\n", "740.0     11628\n", "505.0     11601\n", "518.0     11492\n", "516.0     11338\n", "744.0     11316\n", "742.0     11293\n", "515.0     10973\n", "743.0     10942\n", "514.0     10584\n", "513.0     10515\n", "746.0     10299\n", "511.0     10255\n", "745.0     10254\n", "502.0     10030\n", "510.0      9939\n", "747.0      9785\n", "509.0      9585\n", "748.0      9412\n", "507.0      9209\n", "750.0      9088\n", "506.0      9087\n", "504.0      8816\n", "503.0      8566\n", "751.0      8411\n", "752.0      8252\n", "753.0      7922\n", "754.0      7796\n", "755.0      7547\n", "756.0      7335\n", "757.0      7277\n", "758.0      7029\n", "759.0      6706\n", "762.0      6595\n", "760.0      6560\n", "761.0      6407\n", "763.0      6105\n", "766.0      5793\n", "764.0      5791\n", "765.0      5756\n", "767.0      5337\n", "768.0      5208\n", "769.0      5054\n", "770.0      4974\n", "771.0      4773\n", "772.0      4735\n", "773.0      4618\n", "774.0      4411\n", "775.0      4356\n", "776.0      4135\n", "777.0      4092\n", "780.0      3943\n", "778.0      3890\n", "779.0      3859\n", "781.0      3741\n", "782.0      3719\n", "499.0      3618\n", "783.0      3559\n", "784.0      3446\n", "497.0      3295\n", "787.0      3278\n", "785.0      3231\n", "786.0      3125\n", "788.0      3078\n", "496.0      2993\n", "492.0      2973\n", "790.0      2959\n", "791.0      2910\n", "494.0      2882\n", "789.0      2872\n", "793.0      2826\n", "792.0      2797\n", "794.0      2610\n", "500.0      2579\n", "798.0      2569\n", "795.0      2561\n", "488.0      2552\n", "797.0      2486\n", "796.0      2485\n", "498.0      2480\n", "490.0      2411\n", "800.0      2350\n", "481.0      2322\n", "495.0      2293\n", "799.0      2292\n", "491.0      2286\n", "479.0      2259\n", "853.0      2229\n", "493.0      2170\n", "803.0      2151\n", "487.0      2143\n", "485.0      2136\n", "801.0      2085\n", "489.0      2043\n", "483.0      2029\n", "805.0      1974\n", "804.0      1962\n", "802.0      1936\n", "482.0      1896\n", "480.0      1850\n", "808.0      1849\n", "486.0      1830\n", "806.0      1816\n", "484.0      1797\n", "809.0      1750\n", "807.0      1742\n", "812.0      1711\n", "810.0      1664\n", "478.0      1642\n", "811.0      1629\n", "476.0      1615\n", "477.0      1586\n", "814.0      1548\n", "474.0      1539\n", "813.0      1537\n", "816.0      1482\n", "475.0      1447\n", "472.0      1443\n", "471.0      1431\n", "815.0      1420\n", "817.0      1349\n", "469.0      1340\n", "473.0      1336\n", "468.0      1324\n", "470.0      1317\n", "819.0      1309\n", "820.0      1303\n", "818.0      1300\n", "821.0      1257\n", "823.0      1235\n", "822.0      1231\n", "467.0      1200\n", "824.0      1163\n", "465.0      1133\n", "826.0      1124\n", "466.0      1121\n", "464.0      1115\n", "825.0      1099\n", "462.0      1067\n", "827.0      1034\n", "828.0      1010\n", "461.0       996\n", "829.0       993\n", "830.0       986\n", "463.0       972\n", "834.0       961\n", "831.0       938\n", "460.0       931\n", "832.0       921\n", "833.0       913\n", "459.0       905\n", "835.0       864\n", "839.0       854\n", "838.0       833\n", "836.0       828\n", "841.0       826\n", "458.0       820\n", "844.0       809\n", "837.0       799\n", "842.0       793\n", "456.0       789\n", "840.0       786\n", "457.0       763\n", "847.0       759\n", "845.0       759\n", "990.0       751\n", "846.0       742\n", "455.0       726\n", "843.0       706\n", "454.0       700\n", "851.0       700\n", "852.0       692\n", "850.0       688\n", "848.0       675\n", "849.0       666\n", "451.0       654\n", "453.0       632\n", "452.0       631\n", "450.0       599\n", "856.0       568\n", "855.0       564\n", "449.0       560\n", "448.0       550\n", "854.0       548\n", "447.0       541\n", "858.0       531\n", "857.0       525\n", "859.0       516\n", "861.0       503\n", "862.0       501\n", "445.0       500\n", "446.0       490\n", "864.0       487\n", "443.0       486\n", "865.0       480\n", "867.0       480\n", "860.0       471\n", "863.0       470\n", "866.0       463\n", "872.0       446\n", "871.0       445\n", "870.0       438\n", "869.0       429\n", "875.0       426\n", "442.0       417\n", "444.0       417\n", "868.0       416\n", "440.0       405\n", "877.0       405\n", "873.0       401\n", "876.0       396\n", "441.0       394\n", "439.0       389\n", "874.0       386\n", "879.0       385\n", "880.0       382\n", "881.0       379\n", "436.0       370\n", "878.0       367\n", "882.0       352\n", "883.0       347\n", "438.0       344\n", "885.0       340\n", "437.0       332\n", "884.0       328\n", "886.0       324\n", "435.0       320\n", "433.0       307\n", "434.0       303\n", "890.0       294\n", "889.0       294\n", "425.0       283\n", "888.0       277\n", "432.0       275\n", "887.0       275\n", "891.0       267\n", "892.0       265\n", "430.0       265\n", "896.0       255\n", "431.0       253\n", "427.0       252\n", "894.0       249\n", "895.0       248\n", "428.0       241\n", "893.0       238\n", "899.0       238\n", "429.0       233\n", "904.0       231\n", "898.0       222\n", "897.0       218\n", "903.0       211\n", "900.0       210\n", "300.0       205\n", "422.0       196\n", "902.0       194\n", "426.0       194\n", "905.0       187\n", "906.0       184\n", "907.0       180\n", "421.0       178\n", "901.0       172\n", "423.0       169\n", "420.0       162\n", "908.0       162\n", "424.0       160\n", "913.0       159\n", "909.0       158\n", "916.0       156\n", "912.0       151\n", "911.0       150\n", "418.0       149\n", "910.0       149\n", "419.0       148\n", "416.0       143\n", "914.0       137\n", "414.0       135\n", "917.0       132\n", "417.0       132\n", "921.0       131\n", "919.0       130\n", "918.0       124\n", "920.0       121\n", "415.0       118\n", "412.0       118\n", "915.0       117\n", "922.0       116\n", "413.0       115\n", "925.0       114\n", "926.0       113\n", "407.0       105\n", "924.0       105\n", "927.0       105\n", "928.0       101\n", "409.0       100\n", "411.0        99\n", "923.0        98\n", "410.0        96\n", "930.0        91\n", "408.0        87\n", "929.0        86\n", "931.0        85\n", "934.0        84\n", "932.0        81\n", "939.0        78\n", "403.0        72\n", "933.0        70\n", "936.0        68\n", "406.0        67\n", "404.0        67\n", "401.0        64\n", "946.0        63\n", "944.0        63\n", "935.0        63\n", "402.0        62\n", "937.0        62\n", "405.0        61\n", "940.0        61\n", "400.0        59\n", "942.0        57\n", "948.0        57\n", "396.0        56\n", "938.0        56\n", "941.0        54\n", "947.0        52\n", "943.0        52\n", "949.0        52\n", "395.0        52\n", "398.0        50\n", "945.0        50\n", "950.0        50\n", "345.0        50\n", "951.0        49\n", "399.0        47\n", "955.0        43\n", "346.0        42\n", "957.0        42\n", "357.0        41\n", "354.0        41\n", "353.0        40\n", "953.0        40\n", "391.0        39\n", "344.0        39\n", "350.0        39\n", "343.0        38\n", "956.0        38\n", "975.0        38\n", "397.0        37\n", "960.0        37\n", "360.0        36\n", "385.0        36\n", "954.0        36\n", "961.0        36\n", "358.0        35\n", "355.0        35\n", "390.0        35\n", "388.0        35\n", "952.0        35\n", "381.0        35\n", "389.0        35\n", "356.0        35\n", "963.0        34\n", "348.0        34\n", "359.0        33\n", "971.0        33\n", "339.0        33\n", "347.0        32\n", "959.0        32\n", "340.0        32\n", "364.0        32\n", "352.0        31\n", "966.0        31\n", "968.0        31\n", "383.0        30\n", "392.0        30\n", "342.0        30\n", "379.0        29\n", "382.0        28\n", "958.0        28\n", "341.0        28\n", "387.0        28\n", "384.0        28\n", "962.0        27\n", "965.0        27\n", "361.0        27\n", "970.0        27\n", "386.0        26\n", "394.0        25\n", "368.0        25\n", "366.0        25\n", "376.0        25\n", "967.0        24\n", "380.0        24\n", "973.0        24\n", "978.0        24\n", "338.0        24\n", "974.0        23\n", "964.0        23\n", "349.0        23\n", "972.0        23\n", "977.0        23\n", "337.0        22\n", "980.0        22\n", "983.0        22\n", "369.0        22\n", "377.0        22\n", "976.0        21\n", "393.0        21\n", "378.0        21\n", "982.0        21\n", "367.0        21\n", "363.0        20\n", "373.0        20\n", "986.0        20\n", "984.0        20\n", "351.0        19\n", "969.0        18\n", "372.0        18\n", "987.0        18\n", "365.0        17\n", "362.0        17\n", "375.0        17\n", "985.0        15\n", "309.0        15\n", "336.0        15\n", "981.0        15\n", "326.0        14\n", "979.0        14\n", "331.0        13\n", "989.0        13\n", "334.0        13\n", "374.0        13\n", "335.0        12\n", "371.0        12\n", "329.0        12\n", "328.0        11\n", "308.0        11\n", "370.0        11\n", "323.0        11\n", "333.0        11\n", "988.0        10\n", "325.0        10\n", "314.0         9\n", "320.0         9\n", "304.0         9\n", "330.0         9\n", "313.0         8\n", "303.0         8\n", "315.0         8\n", "316.0         8\n", "324.0         7\n", "321.0         7\n", "301.0         7\n", "322.0         7\n", "332.0         7\n", "310.0         6\n", "311.0         6\n", "302.0         6\n", "327.0         5\n", "312.0         5\n", "306.0         5\n", "319.0         5\n", "318.0         4\n", "307.0         4\n", "305.0         4\n", "317.0         4\n", "Name: count, dtype: int64\n", "==============================\n", "Debt-To-Income Ratio\n", "['10%' '38.64%' '9.43%' ... '1986.02%' '1154.55%' '21215.75%']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub data rate exceeded.\n", "The Jupyter server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--ServerApp.iopub_data_rate_limit`.\n", "\n", "Current values:\n", "ServerApp.iopub_data_rate_limit=1000000.0 (bytes/sec)\n", "ServerApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Zip Code\n", "112xx    267102\n", "770xx    263682\n", "300xx    260429\n", "331xx    245496\n", "606xx    235017\n", "750xx    221895\n", "330xx    220306\n", "900xx    212639\n", "104xx    211708\n", "945xx    199190\n", "891xx    184790\n", "070xx    173834\n", "782xx    173029\n", "917xx    169135\n", "925xx    155302\n", "117xx    148735\n", "334xx    147507\n", "604xx    143288\n", "301xx    142456\n", "100xx    141274\n", "191xx    139928\n", "302xx    136841\n", "481xx    135367\n", "852xx    132236\n", "207xx    130709\n", "853xx    127592\n", "212xx    127353\n", "760xx    126134\n", "080xx    125503\n", "773xx    124497\n", "913xx    124160\n", "923xx    123716\n", "328xx    122845\n", "921xx    122665\n", "850xx    120631\n", "441xx    116693\n", "967xx    114466\n", "775xx    113653\n", "601xx    112888\n", "114xx    111780\n", "774xx    111693\n", "021xx    111211\n", "752xx    108082\n", "303xx    107675\n", "327xx    107554\n", "113xx    107448\n", "600xx    106175\n", "480xx    105833\n", "928xx    105434\n", "322xx    105361\n", "785xx    104008\n", "920xx    103279\n", "902xx    101830\n", "926xx    100601\n", "333xx    100186\n", "956xx     98602\n", "761xx     97508\n", "802xx     97098\n", "786xx     94471\n", "953xx     93602\n", "275xx     93147\n", "347xx     92849\n", "980xx     91021\n", "751xx     89884\n", "282xx     89837\n", "283xx     89271\n", "296xx     89150\n", "370xx     88285\n", "462xx     87584\n", "190xx     86196\n", "958xx     84766\n", "336xx     84042\n", "335xx     83579\n", "840xx     82673\n", "482xx     81819\n", "338xx     81714\n", "088xx     81156\n", "339xx     80801\n", "800xx     80772\n", "787xx     80285\n", "799xx     79649\n", "294xx     79508\n", "337xx     77690\n", "890xx     77582\n", "554xx     76071\n", "432xx     75903\n", "346xx     75517\n", "951xx     75440\n", "381xx     74759\n", "605xx     73366\n", "922xx     73094\n", "483xx     72177\n", "440xx     71916\n", "631xx     71256\n", "857xx     71249\n", "765xx     68883\n", "430xx     68541\n", "028xx     68223\n", "201xx     68122\n", "342xx     67821\n", "325xx     67563\n", "210xx     67532\n", "295xx     67356\n", "906xx     66267\n", "115xx     66210\n", "553xx     65175\n", "982xx     65150\n", "234xx     64958\n", "532xx     64873\n", "064xx     64868\n", "452xx     63315\n", "280xx     63097\n", "060xx     62809\n", "329xx     62366\n", "018xx     62334\n", "809xx     62236\n", "919xx     61122\n", "305xx     60876\n", "983xx     60562\n", "907xx     60193\n", "871xx     60109\n", "700xx     60100\n", "630xx     59800\n", "371xx     59686\n", "941xx     59664\n", "152xx     59383\n", "402xx     59334\n", "320xx     58924\n", "970xx     58195\n", "940xx     57893\n", "932xx     57423\n", "463xx     57211\n", "972xx     56811\n", "935xx     56729\n", "142xx     56606\n", "705xx     56232\n", "930xx     56138\n", "321xx     55996\n", "640xx     55457\n", "232xx     55443\n", "027xx     54584\n", "731xx     54421\n", "103xx     53740\n", "730xx     53545\n", "490xx     53404\n", "641xx     53296\n", "950xx     53072\n", "200xx     52803\n", "801xx     52581\n", "981xx     52557\n", "551xx     52151\n", "352xx     51049\n", "933xx     50667\n", "077xx     50509\n", "281xx     50473\n", "740xx     50440\n", "349xx     50404\n", "105xx     50293\n", "146xx     49712\n", "286xx     49699\n", "272xx     49220\n", "780xx     48989\n", "180xx     48808\n", "285xx     48666\n", "208xx     48544\n", "310xx     48482\n", "350xx     48437\n", "071xx     48275\n", "076xx     48087\n", "373xx     47791\n", "109xx     47648\n", "762xx     47548\n", "278xx     46961\n", "378xx     46942\n", "908xx     46727\n", "344xx     46699\n", "450xx     46612\n", "937xx     46485\n", "276xx     46475\n", "170xx     46122\n", "550xx     45915\n", "211xx     45687\n", "442xx     45516\n", "952xx     45502\n", "461xx     45499\n", "410xx     45066\n", "531xx     44783\n", "973xx     44602\n", "290xx     44525\n", "023xx     44422\n", "217xx     44402\n", "206xx     44306\n", "986xx     43870\n", "431xx     43805\n", "720xx     43534\n", "125xx     43507\n", "029xx     43499\n", "727xx     43351\n", "841xx     42941\n", "380xx     42786\n", "781xx     42707\n", "236xx     42318\n", "386xx     42306\n", "936xx     42290\n", "851xx     41975\n", "087xx     41226\n", "974xx     40980\n", "454xx     40628\n", "984xx     40616\n", "959xx     40587\n", "797xx     40415\n", "453xx     40395\n", "019xx     40183\n", "805xx     40133\n", "681xx     40113\n", "968xx     40093\n", "633xx     39911\n", "660xx     39842\n", "488xx     39801\n", "199xx     39662\n", "460xx     39569\n", "293xx     39506\n", "287xx     39459\n", "985xx     39406\n", "010xx     39320\n", "150xx     39084\n", "395xx     39026\n", "704xx     39026\n", "284xx     38971\n", "701xx     38593\n", "365xx     38310\n", "323xx     38310\n", "130xx     38161\n", "140xx     37802\n", "238xx     37627\n", "622xx     37270\n", "221xx     37136\n", "067xx     36630\n", "707xx     36628\n", "306xx     36617\n", "297xx     36276\n", "291xx     36164\n", "292xx     36109\n", "194xx     36080\n", "465xx     35756\n", "494xx     35449\n", "074xx     35401\n", "376xx     34803\n", "245xx     34712\n", "934xx     34696\n", "351xx     34684\n", "535xx     34546\n", "672xx     34535\n", "307xx     34489\n", "197xx     34433\n", "446xx     34353\n", "927xx     34225\n", "946xx     34076\n", "372xx     33912\n", "530xx     33434\n", "754xx     33400\n", "721xx     33371\n", "954xx     33364\n", "030xx     33316\n", "324xx     33313\n", "484xx     33275\n", "151xx     33212\n", "444xx     33117\n", "741xx     32809\n", "708xx     32632\n", "068xx     32474\n", "273xx     32398\n", "120xx     32067\n", "436xx     32028\n", "073xx     31739\n", "065xx     31703\n", "220xx     31687\n", "394xx     31460\n", "317xx     31440\n", "757xx     31425\n", "379xx     31189\n", "078xx     30966\n", "366xx     30709\n", "368xx     30574\n", "326xx     30478\n", "356xx     30477\n", "456xx     30346\n", "240xx     30300\n", "315xx     30298\n", "377xx     30239\n", "015xx     30058\n", "231xx     29952\n", "360xx     29811\n", "894xx     29773\n", "914xx     29678\n", "274xx     29542\n", "711xx     29540\n", "313xx     29486\n", "448xx     29303\n", "957xx     29068\n", "061xx     29064\n", "451xx     28949\n", "756xx     28839\n", "549xx     28692\n", "662xx     28667\n", "495xx     28662\n", "341xx     28592\n", "458xx     28481\n", "712xx     28228\n", "995xx     28130\n", "173xx     28079\n", "066xx     28040\n", "784xx     27922\n", "107xx     27853\n", "403xx     27833\n", "223xx     27436\n", "063xx     27414\n", "233xx     27377\n", "082xx     27357\n", "144xx     27327\n", "121xx     27202\n", "895xx     27063\n", "038xx     27019\n", "390xx     26843\n", "209xx     26816\n", "856xx     26752\n", "083xx     26719\n", "486xx     26664\n", "145xx     26348\n", "119xx     26309\n", "806xx     26253\n", "314xx     26173\n", "443xx     26080\n", "492xx     25922\n", "156xx     25913\n", "703xx     25773\n", "111xx     25738\n", "020xx     25687\n", "939xx     25675\n", "136xx     25610\n", "363xx     25593\n", "235xx     25490\n", "383xx     25413\n", "992xx     25238\n", "960xx     25175\n", "924xx     25126\n", "391xx     25122\n", "392xx     25117\n", "359xx     25057\n", "836xx     24793\n", "361xx     24714\n", "778xx     24664\n", "277xx     24616\n", "794xx     24554\n", "224xx     24500\n", "086xx     24380\n", "468xx     24157\n", "993xx     24088\n", "473xx     23980\n", "706xx     23963\n", "193xx     23954\n", "388xx     23946\n", "075xx     23808\n", "435xx     23595\n", "405xx     23480\n", "916xx     23470\n", "657xx     23465\n", "724xx     23436\n", "880xx     23378\n", "469xx     23198\n", "949xx     23153\n", "471xx     22913\n", "971xx     22894\n", "467xx     22741\n", "072xx     22740\n", "319xx     22654\n", "354xx     22621\n", "225xx     22608\n", "776xx     22494\n", "810xx     22427\n", "357xx     22418\n", "610xx     22326\n", "128xx     22301\n", "271xx     22217\n", "620xx     22216\n", "183xx     22174\n", "544xx     22072\n", "148xx     22042\n", "975xx     22021\n", "910xx     21988\n", "270xx     21863\n", "230xx     21710\n", "017xx     21648\n", "722xx     21621\n", "308xx     21510\n", "309xx     21428\n", "085xx     21349\n", "864xx     21315\n", "759xx     21274\n", "479xx     21186\n", "132xx     20995\n", "912xx     20884\n", "189xx     20777\n", "175xx     20656\n", "110xx     20615\n", "729xx     20521\n", "989xx     20486\n", "298xx     20361\n", "783xx     20354\n", "559xx     20200\n", "735xx     20199\n", "011xx     20144\n", "184xx     19941\n", "652xx     19916\n", "658xx     19899\n", "229xx     19823\n", "040xx     19556\n", "362xx     19539\n", "241xx     19471\n", "791xx     19461\n", "304xx     19400\n", "618xx     19376\n", "198xx     19375\n", "226xx     19228\n", "496xx     19050\n", "493xx     19011\n", "134xx     18985\n", "563xx     18855\n", "141xx     18726\n", "299xx     18720\n", "196xx     18664\n", "316xx     18637\n", "358xx     18629\n", "385xx     18627\n", "178xx     18528\n", "186xx     18494\n", "374xx     18250\n", "400xx     18215\n", "218xx     18144\n", "421xx     18025\n", "166xx     17958\n", "744xx     17956\n", "439xx     17948\n", "054xx     17881\n", "977xx     17853\n", "611xx     17839\n", "014xx     17800\n", "560xx     17691\n", "016xx     17689\n", "723xx     17599\n", "161xx     17566\n", "882xx     17517\n", "123xx     17503\n", "863xx     17491\n", "648xx     17455\n", "032xx     17439\n", "433xx     17182\n", "714xx     17104\n", "656xx     16968\n", "129xx     16943\n", "393xx     16941\n", "447xx     16930\n", "713xx     16891\n", "948xx     16837\n", "131xx     16749\n", "279xx     16742\n", "629xx     16695\n", "716xx     16649\n", "763xx     16508\n", "195xx     16447\n", "171xx     16443\n", "312xx     16388\n", "474xx     16372\n", "790xx     16359\n", "547xx     16359\n", "437xx     16346\n", "719xx     16239\n", "445xx     16221\n", "181xx     16104\n", "172xx     16018\n", "685xx     16001\n", "024xx     16000\n", "628xx     15991\n", "182xx     15963\n", "216xx     15951\n", "710xx     15913\n", "988xx     15760\n", "147xx     15712\n", "472xx     15474\n", "748xx     15449\n", "767xx     15373\n", "153xx     15294\n", "420xx     15249\n", "081xx     15127\n", "577xx     15048\n", "661xx     15017\n", "031xx     14975\n", "788xx     14964\n", "497xx     14884\n", "903xx     14874\n", "174xx     14871\n", "124xx     14769\n", "478xx     14711\n", "539xx     14679\n", "242xx     14565\n", "875xx     14523\n", "537xx     14520\n", "154xx     14515\n", "546xx     14461\n", "870xx     14450\n", "401xx     14445\n", "612xx     14411\n", "617xx     14407\n", "384xx     14398\n", "397xx     14389\n", "165xx     14376\n", "254xx     14349\n", "062xx     14329\n", "485xx     14324\n", "766xx     14306\n", "541xx     14305\n", "355xx     14232\n", "122xx     14083\n", "489xx     14081\n", "187xx     14077\n", "804xx     14036\n", "404xx     14005\n", "670xx     13990\n", "427xx     13944\n", "847xx     13919\n", "464xx     13869\n", "160xx     13845\n", "996xx     13788\n", "571xx     13749\n", "243xx     13720\n", "838xx     13710\n", "905xx     13706\n", "487xx     13688\n", "666xx     13637\n", "026xx     13612\n", "608xx     13587\n", "837xx     13437\n", "116xx     13412\n", "434xx     13392\n", "846xx     13391\n", "422xx     13311\n", "222xx     13298\n", "133xx     13246\n", "844xx     13194\n", "177xx     13115\n", "815xx     13061\n", "477xx     12987\n", "137xx     12907\n", "808xx     12884\n", "548xx     12883\n", "769xx     12873\n", "779xx     12861\n", "860xx     12819\n", "609xx     12781\n", "755xx     12726\n", "820xx     12717\n", "597xx     12696\n", "176xx     12591\n", "185xx     12539\n", "179xx     12332\n", "138xx     12327\n", "423xx     12289\n", "466xx     12252\n", "911xx     12251\n", "615xx     12248\n", "598xx     12234\n", "680xx     12104\n", "237xx     12067\n", "616xx     12042\n", "244xx     11937\n", "069xx     11875\n", "012xx     11867\n", "816xx     11769\n", "042xx     11766\n", "228xx     11762\n", "163xx     11727\n", "079xx     11719\n", "625xx     11694\n", "557xx     11652\n", "168xx     11633\n", "777xx     11614\n", "636xx     11578\n", "978xx     11577\n", "764xx     11544\n", "793xx     11495\n", "387xx     11453\n", "626xx     11448\n", "796xx     11379\n", "997xx     11372\n", "728xx     11351\n", "457xx     11331\n", "543xx     11268\n", "396xx     11222\n", "613xx     11198\n", "726xx     11197\n", "655xx     11197\n", "127xx     11164\n", "565xx     11129\n", "718xx     11115\n", "627xx     11107\n", "367xx     11062\n", "570xx     11062\n", "931xx     11031\n", "717xx     11017\n", "650xx     10917\n", "159xx     10888\n", "389xx     10764\n", "638xx     10752\n", "044xx     10745\n", "025xx     10706\n", "915xx     10620\n", "265xx     10515\n", "411xx     10483\n", "637xx     10435\n", "591xx     10432\n", "364xx     10291\n", "664xx     10285\n", "049xx     10256\n", "743xx     10228\n", "674xx     10178\n", "424xx     10166\n", "667xx     10153\n", "219xx     10152\n", "955xx     10050\n", "833xx     10001\n", "475xx      9868\n", "990xx      9868\n", "540xx      9854\n", "607xx      9746\n", "758xx      9698\n", "688xx      9651\n", "498xx      9614\n", "398xx      9606\n", "288xx      9456\n", "614xx      9425\n", "874xx      9379\n", "534xx      9288\n", "407xx      9184\n", "843xx      9093\n", "239xx      9088\n", "749xx      9064\n", "455xx      9033\n", "382xx      9028\n", "164xx      9017\n", "564xx      8973\n", "826xx      8964\n", "665xx      8918\n", "624xx      8903\n", "585xx      8889\n", "215xx      8867\n", "647xx      8826\n", "594xx      8737\n", "725xx      8691\n", "599xx      8607\n", "449xx      8604\n", "795xx      8594\n", "944xx      8593\n", "089xx      8561\n", "562xx      8521\n", "126xx      8513\n", "470xx      8510\n", "675xx      8461\n", "318xx      8460\n", "834xx      8414\n", "157xx      8398\n", "678xx      8282\n", "653xx      8224\n", "855xx      8068\n", "108xx      8063\n", "671xx      8027\n", "734xx      7970\n", "476xx      7952\n", "961xx      7876\n", "645xx      7832\n", "143xx      7805\n", "260xx      7722\n", "227xx      7718\n", "106xx      7647\n", "581xx      7613\n", "158xx      7609\n", "832xx      7577\n", "737xx      7571\n", "255xx      7493\n", "491xx      7458\n", "768xx      7274\n", "747xx      7193\n", "587xx      7188\n", "558xx      7167\n", "188xx      7126\n", "619xx      7073\n", "623xx      7050\n", "056xx      6996\n", "139xx      6989\n", "644xx      6987\n", "253xx      6955\n", "542xx      6880\n", "904xx      6835\n", "883xx      6805\n", "654xx      6798\n", "918xx      6796\n", "811xx      6780\n", "261xx      6773\n", "013xx      6755\n", "812xx      6743\n", "991xx      6701\n", "057xx      6695\n", "037xx      6665\n", "155xx      6588\n", "034xx      6585\n", "639xx      6579\n", "745xx      6561\n", "976xx      6549\n", "814xx      6505\n", "162xx      6490\n", "829xx      6441\n", "135xx      6421\n", "214xx      6415\n", "897xx      6384\n", "881xx      6307\n", "947xx      6275\n", "898xx      6080\n", "425xx      6013\n", "118xx      6008\n", "687xx      5985\n", "807xx      5975\n", "246xx      5892\n", "545xx      5841\n", "415xx      5836\n", "582xx      5785\n", "789xx      5718\n", "596xx      5571\n", "033xx      5549\n", "827xx      5538\n", "409xx      5524\n", "590xx      5393\n", "050xx      5386\n", "798xx      5385\n", "438xx      5376\n", "580xx      5366\n", "859xx      5289\n", "258xx      5286\n", "561xx      5257\n", "673xx      5244\n", "803xx      5198\n", "263xx      5172\n", "566xx      5165\n", "041xx      5146\n", "873xx      5105\n", "101xx      5090\n", "634xx      5083\n", "149xx      5074\n", "813xx      5061\n", "043xx      5050\n", "058xx      5041\n", "084xx      5023\n", "651xx      4993\n", "646xx      4968\n", "262xx      4858\n", "588xx      4785\n", "691xx      4782\n", "406xx      4771\n", "567xx      4728\n", "602xx      4686\n", "693xx      4654\n", "047xx      4627\n", "689xx      4625\n", "683xx      4521\n", "573xx      4520\n", "035xx      4513\n", "169xx      4493\n", "046xx      4469\n", "251xx      4430\n", "943xx      4408\n", "499xx      4345\n", "603xx      4345\n", "668xx      4330\n", "412xx      4288\n", "746xx      4213\n", "572xx      4066\n", "167xx      4060\n", "426xx      4022\n", "686xx      4015\n", "736xx      3974\n", "247xx      3951\n", "676xx      3949\n", "250xx      3948\n", "635xx      3896\n", "684xx      3888\n", "257xx      3776\n", "824xx      3685\n", "877xx      3674\n", "264xx      3667\n", "574xx      3664\n", "998xx      3637\n", "586xx      3324\n", "865xx      3288\n", "052xx      3272\n", "538xx      3253\n", "845xx      3239\n", "416xx      3237\n", "575xx      3206\n", "417xx      3151\n", "252xx      3050\n", "259xx      2966\n", "835xx      2953\n", "039xx      2934\n", "053xx      2932\n", "267xx      2849\n", "289xx      2848\n", "828xx      2829\n", "045xx      2788\n", "592xx      2771\n", "825xx      2766\n", "593xx      2700\n", "051xx      2691\n", "256xx      2643\n", "583xx      2634\n", "413xx      2629\n", "249xx      2606\n", "677xx      2548\n", "408xx      2517\n", "792xx      2513\n", "584xx      2494\n", "418xx      2360\n", "679xx      2358\n", "738xx      2214\n", "048xx      2135\n", "822xx      2068\n", "739xx      1997\n", "979xx      1880\n", "266xx      1867\n", "369xx      1831\n", "268xx      1819\n", "248xx      1800\n", "414xx      1742\n", "999xx      1715\n", "595xx      1673\n", "669xx      1662\n", "884xx      1622\n", "831xx      1509\n", "823xx      1479\n", "994xx      1445\n", "690xx      1365\n", "772xx      1331\n", "022xx      1291\n", "036xx      1251\n", "878xx      1207\n", "830xx      1202\n", "893xx      1180\n", "102xx      1154\n", "879xx      1143\n", "576xx      1038\n", "963xx       979\n", "556xx       864\n", "009xx       809\n", "332xx       786\n", "969xx       770\n", "000xx       646\n", "090xx       633\n", "091xx       597\n", "007xx       595\n", "753xx       535\n", "006xx       523\n", "002xx       498\n", "692xx       492\n", "500xx       484\n", "001xx       455\n", "059xx       454\n", "695xx       441\n", "503xx       375\n", "008xx       374\n", "962xx       330\n", "096xx       315\n", "502xx       286\n", "311xx       276\n", "527xx       265\n", "501xx       263\n", "202xx       225\n", "522xx       220\n", "733xx       219\n", "515xx       216\n", "524xx       213\n", "528xx       210\n", "523xx       205\n", "094xx       204\n", "966xx       197\n", "520xx       194\n", "555xx       187\n", "345xx       183\n", "203xx       173\n", "507xx       169\n", "511xx       167\n", "505xx       160\n", "205xx       157\n", "506xx       156\n", "526xx       156\n", "504xx       155\n", "510xx       147\n", "942xx       147\n", "861xx       146\n", "092xx       145\n", "340xx       144\n", "525xx       140\n", "097xx       137\n", "204xx       132\n", "987xx       128\n", "343xx       123\n", "901xx       120\n", "965xx       115\n", "098xx       111\n", "858xx       103\n", "521xx       103\n", "929xx        96\n", "213xx        96\n", "005xx        94\n", "095xx        87\n", "854xx        86\n", "513xx        74\n", "821xx        71\n", "353xx        68\n", "702xx        67\n", "552xx        67\n", "938xx        64\n", "909xx        60\n", "632xx        59\n", "892xx        58\n", "888xx        54\n", "621xx        54\n", "742xx        52\n", "514xx        51\n", "093xx        51\n", "867xx        51\n", "348xx        51\n", "508xx        49\n", "885xx        48\n", "512xx        47\n", "533xx        44\n", "568xx        42\n", "682xx        42\n", "732xx        42\n", "003xx        41\n", "516xx        41\n", "964xx        41\n", "642xx        41\n", "192xx        40\n", "876xx        39\n", "459xx        38\n", "862xx        37\n", "696xx        35\n", "399xx        35\n", "771xx        35\n", "269xx        34\n", "643xx        32\n", "709xx        31\n", "375xx        31\n", "848xx        30\n", "715xx        30\n", "896xx        30\n", "536xx        29\n", "649xx        29\n", "055xx        29\n", "872xx        26\n", "842xx        26\n", "663xx        25\n", "889xx        24\n", "819xx        24\n", "868xx        23\n", "886xx        22\n", "694xx        22\n", "817xx        21\n", "099xx        19\n", "839xx        19\n", "899xx        18\n", "569xx        18\n", "518xx        18\n", "589xx        18\n", "697xx        18\n", "428xx        17\n", "887xx        17\n", "849xx        16\n", "866xx        16\n", "818xx        16\n", "578xx        14\n", "517xx        14\n", "519xx        14\n", "659xx        13\n", "698xx        12\n", "509xx        11\n", "529xx        11\n", "429xx        11\n", "419xx        11\n", "004xx        11\n", "869xx        10\n", "699xx         9\n", "579xx         3\n", "09Oxx         1\n", "Name: count, dtype: int64\n", "==============================\n", "State\n", "['NM' 'MA' 'MD' 'NY' 'IN' 'CO' 'KY' 'LA' 'PA' 'AL' 'TN' 'TX' 'OH' 'VA'\n", " 'MO' 'MN' 'CT' 'CA' 'FL' 'GA' 'NE' 'KS' 'AR' 'WI' 'AZ' 'WA' 'OR' 'NC'\n", " 'WV' 'UT' 'NJ' 'AK' 'SC' 'VT' 'MS' 'DE' 'MI' 'IA' 'IL' 'OK' 'ME' 'HI'\n", " 'NH' 'DC' 'RI' 'NV' 'ND' 'MT' 'WY' 'ID' 'SD' nan]\n", "State\n", "CA    3242169\n", "TX    2495511\n", "FL    2167584\n", "NY    1991179\n", "GA    1083614\n", "PA    1047694\n", "OH    1011312\n", "IL    1001046\n", "NC     863860\n", "NJ     853305\n", "MI     755641\n", "VA     738834\n", "MD     594758\n", "AZ     588880\n", "TN     576019\n", "MA     546771\n", "IN     517324\n", "WA     497850\n", "MO     496149\n", "AL     493226\n", "CO     470021\n", "SC     468381\n", "LA     421634\n", "WI     361665\n", "MN     359235\n", "KY     350055\n", "CT     339512\n", "NV     333579\n", "OK     306065\n", "AR     291237\n", "OR     283553\n", "MS     277321\n", "KS     230571\n", "UT     179421\n", "NM     165466\n", "HI     155688\n", "NH     117584\n", "NE     112814\n", "RI     112160\n", "WV     106229\n", "DE      94147\n", "ID      80252\n", "ME      78839\n", "MT      71212\n", "AK      60427\n", "SD      56898\n", "DC      52807\n", "VT      51497\n", "WY      49645\n", "ND      47622\n", "IA        456\n", "Name: count, dtype: int64\n", "==============================\n", "Employment Length\n", "['4 years' '< 1 year' '1 year' '3 years' '2 years' '10+ years' '9 years'\n", " '5 years' '7 years' '6 years' '8 years' nan]\n", "Employment Length\n", "< 1 year     22994315\n", "5 years       2279466\n", "10+ years      416384\n", "1 year         267840\n", "2 years        199204\n", "3 years        177344\n", "4 years        121623\n", "6 years         71625\n", "8 years         65965\n", "7 years         55666\n", "9 years         47954\n", "Name: count, dtype: int64\n", "==============================\n", "Policy Code\n", "[ 0.  2. nan]\n", "Policy Code\n", "0.0    27559694\n", "2.0       88129\n", "Name: count, dtype: int64\n", "==============================\n"]}], "source": ["for col in df:\n", "    print(\"=\"*30)\n", "    print(col)\n", "    print(df[col].unique())\n", "    print(df[col].value_counts())\n", "print(\"=\"*30)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "89d69d9d", "metadata": {}, "outputs": [], "source": ["# Convert date columns to datetime format\n", "df['Application Date'] = pd.to_datetime(df['Application Date'])"]}, {"cell_type": "code", "execution_count": 17, "id": "c6ee084e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Timestamp('2007-05-26 00:00:00')"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Application Date\"][1]"]}, {"cell_type": "code", "execution_count": 18, "id": "cee2152e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earliest: 2007-05-26 00:00:00\n", "Latest: 2018-12-31 00:00:00\n"]}], "source": ["earliest_date = df['Application Date'].min()\n", "latest_date = df['Application Date'].max()\n", "\n", "print(\"Earliest:\", earliest_date)\n", "print(\"Latest:\", latest_date)"]}, {"cell_type": "markdown", "id": "28ea1e9d", "metadata": {}, "source": ["## Filtering loans according to updated risk score (post 2013-11-05)"]}, {"cell_type": "code", "execution_count": 19, "id": "50263312", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2013-11-05 00:00:00\n"]}, {"data": {"text/plain": ["((26243055, 9), (27648741, 9))"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["cutoff_date = pd.to_datetime('2013-11-05')\n", "print(cutoff_date)\n", "df_post_nov5 = df[df['Application Date'] > cutoff_date]\n", "df_post_nov5.shape, df.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "7490075c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount Requested</th>\n", "      <th>Application Date</th>\n", "      <th>Loan Title</th>\n", "      <th>Risk_Score</th>\n", "      <th>Debt-To-Income Ratio</th>\n", "      <th>Zip Code</th>\n", "      <th>State</th>\n", "      <th>Employment Length</th>\n", "      <th>Policy Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>755491</th>\n", "      <td>1000.0</td>\n", "      <td>2016-04-01</td>\n", "      <td>other</td>\n", "      <td>NaN</td>\n", "      <td>2.69%</td>\n", "      <td>331xx</td>\n", "      <td>FL</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755492</th>\n", "      <td>4000.0</td>\n", "      <td>2016-04-01</td>\n", "      <td>debt_consolidation</td>\n", "      <td>NaN</td>\n", "      <td>28.26%</td>\n", "      <td>834xx</td>\n", "      <td>ID</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755493</th>\n", "      <td>5000.0</td>\n", "      <td>2016-04-01</td>\n", "      <td>moving</td>\n", "      <td>NaN</td>\n", "      <td>-1%</td>\n", "      <td>648xx</td>\n", "      <td>MO</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755494</th>\n", "      <td>1000.0</td>\n", "      <td>2016-04-01</td>\n", "      <td>moving</td>\n", "      <td>628.0</td>\n", "      <td>21.43%</td>\n", "      <td>380xx</td>\n", "      <td>TN</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755495</th>\n", "      <td>3000.0</td>\n", "      <td>2016-04-01</td>\n", "      <td>Debt consolidation</td>\n", "      <td>NaN</td>\n", "      <td>8.49%</td>\n", "      <td>895xx</td>\n", "      <td>NV</td>\n", "      <td>2 years</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Amount Requested Application Date          Loan Title  Risk_Score  \\\n", "755491            1000.0       2016-04-01               other         NaN   \n", "755492            4000.0       2016-04-01  debt_consolidation         NaN   \n", "755493            5000.0       2016-04-01              moving         NaN   \n", "755494            1000.0       2016-04-01              moving       628.0   \n", "755495            3000.0       2016-04-01  Debt consolidation         NaN   \n", "\n", "       Debt-To-Income Ratio Zip Code State Employment Length  Policy Code  \n", "755491                2.69%    331xx    FL          < 1 year          0.0  \n", "755492               28.26%    834xx    ID          < 1 year          0.0  \n", "755493                  -1%    648xx    MO               NaN          0.0  \n", "755494               21.43%    380xx    TN          < 1 year          0.0  \n", "755495                8.49%    895xx    NV           2 years          2.0  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_post_nov5.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "3cf3335b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(29001, 9)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"Risk_Score\"] > 850].shape"]}, {"cell_type": "code", "execution_count": null, "id": "d733bda6", "metadata": {}, "outputs": [], "source": ["sorted(df[\"Risk_Score\"].unique(), reverse=True)"]}, {"cell_type": "code", "execution_count": null, "id": "9e6ef416", "metadata": {}, "outputs": [], "source": ["sorted(df_post_nov5[\"Risk_Score\"].unique(), reverse=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "d483d3bb", "metadata": {}, "outputs": [], "source": ["df_post_nov5_number_missing = df_post_nov5.isnull().sum()\n", "df_post_nov5_percentage_missing = df_post_nov5_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": 28, "id": "39bcec96", "metadata": {}, "outputs": [{"data": {"text/plain": ["({'Amount Requested': 0.0,\n", "  'Application Date': 0.0,\n", "  'Loan Title': 0.004719925583591672,\n", "  'Risk_Score': 66.90225063050791,\n", "  'Debt-To-Income Ratio': 0.0,\n", "  'Zip Code': 0.0010597227555497013,\n", "  'State': 7.95696266965646e-05,\n", "  'Employment Length': 3.440861918450464,\n", "  'Policy Code': 0.0033202235139748316},\n", " {'Amount Requested': 0,\n", "  'Application Date': 0,\n", "  'Loan Title': 1305,\n", "  'Risk_Score': 18497630,\n", "  'Debt-To-Income Ratio': 0,\n", "  'Zip Code': 293,\n", "  'State': 22,\n", "  'Employment Length': 951355,\n", "  'Policy Code': 918})"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["dict_post_nov5_number_missing = dict_df.copy()\n", "dict_post_nov5_percentage_missing = dict_df.copy()\n", "dict_post_nov5_number_missing.update(dict_number_missing)\n", "dict_post_nov5_percentage_missing.update(dict_percentage_missing)\n", "dict_post_nov5_percentage_missing, dict_post_nov5_number_missing"]}, {"cell_type": "code", "execution_count": 29, "id": "51485f49", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Number of missing values</th>\n", "      <th>Percentage of missing values</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Amount Requested</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Application Date</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Loan Title</th>\n", "      <td>1305.0</td>\n", "      <td>0.004720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Risk_Score</th>\n", "      <td>18497630.0</td>\n", "      <td>66.902251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Debt-To-Income Ratio</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Zip Code</th>\n", "      <td>293.0</td>\n", "      <td>0.001060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>State</th>\n", "      <td>22.0</td>\n", "      <td>0.000080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Employment Length</th>\n", "      <td>951355.0</td>\n", "      <td>3.440862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Policy Code</th>\n", "      <td>918.0</td>\n", "      <td>0.003320</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Number of missing values  Percentage of missing values\n", "Amount Requested                           0.0                      0.000000\n", "Application Date                           0.0                      0.000000\n", "Loan Title                              1305.0                      0.004720\n", "Risk_Score                          18497630.0                     66.902251\n", "Debt-To-Income Ratio                       0.0                      0.000000\n", "Zip Code                                 293.0                      0.001060\n", "State                                     22.0                      0.000080\n", "Employment Length                     951355.0                      3.440862\n", "Policy Code                              918.0                      0.003320"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([dict_post_nov5_number_missing, dict_post_nov5_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "markdown", "id": "b5cfc09b", "metadata": {}, "source": ["## Checking Risk score missing data distribution"]}, {"cell_type": "code", "execution_count": null, "id": "7bca7380", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'na post 2013-11-05': 18461853, 'total post 2013-11-05': 26243055}\n", "Percentage of missing values: 70.35%\n"]}], "source": ["# Empty risk score values post 2013-11-05\n", "len_na_post_nov5 = df_post_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_post_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 38, "id": "c9341ba7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'na post 2013-11-05': 35468, 'total post 2013-11-05': 1402039}\n", "Percentage of missing values: 2.53%\n"]}], "source": ["# Empty risk score values pre 2013-11-05\n", "df_pre_nov5 = df[df['Application Date'] < cutoff_date]\n", "len_na_post_nov5 = df_pre_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_pre_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 39, "id": "98e7288f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'na post 2013-11-05': 309, 'total post 2013-11-05': 3647}\n", "Percentage of missing values: 8.47%\n"]}], "source": ["# Empty risk score values on 2013-11-05\n", "df_pre_nov5 = df[df['Application Date'] == cutoff_date]\n", "len_na_post_nov5 = df_pre_nov5[\"Risk_Score\"].isnull().sum()\n", "len_post_nov5 = len(df_pre_nov5)\n", "print(dict({\"na post 2013-11-05\": len_na_post_nov5, \"total post 2013-11-05\": len_post_nov5}))\n", "print(f\"Percentage of missing values: {len_na_post_nov5 / len_post_nov5 * 100:.2f}%\")"]}, {"cell_type": "markdown", "id": "0bbca6b5", "metadata": {}, "source": ["## Cleaning database"]}, {"cell_type": "code", "execution_count": 51, "id": "6d2fb465", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean = df_post_nov5.dropna()"]}, {"cell_type": "code", "execution_count": 55, "id": "4a1e6033", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7639049, 9)"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["df_post_nov5_clean.shape"]}, {"cell_type": "code", "execution_count": 57, "id": "34ffab97", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount Requested</th>\n", "      <th>Application Date</th>\n", "      <th>Risk_Score</th>\n", "      <th>Policy Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>7.639049e+06</td>\n", "      <td>7639049</td>\n", "      <td>7.639049e+06</td>\n", "      <td>7.639049e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.254396e+04</td>\n", "      <td>2016-09-06 13:08:08.636899072</td>\n", "      <td>6.328021e+02</td>\n", "      <td>3.766437e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.500000e+02</td>\n", "      <td>2013-11-06 00:00:00</td>\n", "      <td>3.000000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.000000e+03</td>\n", "      <td>2015-01-27 00:00:00</td>\n", "      <td>5.920000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000e+04</td>\n", "      <td>2017-04-05 00:00:00</td>\n", "      <td>6.350000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.000000e+04</td>\n", "      <td>2017-10-26 00:00:00</td>\n", "      <td>6.720000e+02</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.400000e+06</td>\n", "      <td>2018-12-31 00:00:00</td>\n", "      <td>9.900000e+02</td>\n", "      <td>2.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.092394e+04</td>\n", "      <td>NaN</td>\n", "      <td>6.504857e+01</td>\n", "      <td>8.671038e-02</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Amount Requested               Application Date    Risk_Score  \\\n", "count      7.639049e+06                        7639049  7.639049e+06   \n", "mean       1.254396e+04  2016-09-06 13:08:08.636899072  6.328021e+02   \n", "min        1.500000e+02            2013-11-06 00:00:00  3.000000e+02   \n", "25%        4.000000e+03            2015-01-27 00:00:00  5.920000e+02   \n", "50%        1.000000e+04            2017-04-05 00:00:00  6.350000e+02   \n", "75%        2.000000e+04            2017-10-26 00:00:00  6.720000e+02   \n", "max        1.400000e+06            2018-12-31 00:00:00  9.900000e+02   \n", "std        1.092394e+04                            NaN  6.504857e+01   \n", "\n", "        Policy Code  \n", "count  7.639049e+06  \n", "mean   3.766437e-03  \n", "min    0.000000e+00  \n", "25%    0.000000e+00  \n", "50%    0.000000e+00  \n", "75%    0.000000e+00  \n", "max    2.000000e+00  \n", "std    8.671038e-02  "]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["df_post_nov5_clean.describe()"]}, {"cell_type": "code", "execution_count": 52, "id": "7c07e11f", "metadata": {}, "outputs": [], "source": ["df_post_nov5_clean_number_missing = df_post_nov5_clean.isnull().sum()\n", "df_post_nov5_clean_percentage_missing = df_post_nov5_clean_number_missing / len(df) * 100"]}, {"cell_type": "code", "execution_count": 53, "id": "f51d3512", "metadata": {}, "outputs": [{"data": {"text/plain": ["({'Amount Requested': 0.0,\n", "  'Application Date': 0.0,\n", "  'Loan Title': 0.0,\n", "  'Risk_Score': 0.0,\n", "  'Debt-To-Income Ratio': 0.0,\n", "  'Zip Code': 0.0,\n", "  'State': 0.0,\n", "  'Employment Length': 0.0,\n", "  'Policy Code': 0.0},\n", " {'Amount Requested': 0,\n", "  'Application Date': 0,\n", "  'Loan Title': 0,\n", "  'Risk_Score': 0,\n", "  'Debt-To-Income Ratio': 0,\n", "  'Zip Code': 0,\n", "  'State': 0,\n", "  'Employment Length': 0,\n", "  'Policy Code': 0})"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["dict_post_nov5_clean_number_missing = dict_df.copy()\n", "dict_post_nov5_clean_percentage_missing = dict_df.copy()\n", "dict_post_nov5_clean_number_missing.update(df_post_nov5_clean_number_missing)\n", "dict_post_nov5_clean_percentage_missing.update(df_post_nov5_clean_percentage_missing)\n", "dict_post_nov5_clean_percentage_missing, dict_post_nov5_clean_number_missing"]}, {"cell_type": "code", "execution_count": 54, "id": "e0319422", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Number of missing values</th>\n", "      <th>Percentage of missing values</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Amount Requested</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Application Date</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Loan Title</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Risk_Score</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Debt-To-Income Ratio</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Zip Code</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>State</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Employment Length</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Policy Code</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Number of missing values  Percentage of missing values\n", "Amount Requested                           0.0                           0.0\n", "Application Date                           0.0                           0.0\n", "Loan Title                                 0.0                           0.0\n", "Risk_Score                                 0.0                           0.0\n", "Debt-To-Income Ratio                       0.0                           0.0\n", "Zip Code                                   0.0                           0.0\n", "State                                      0.0                           0.0\n", "Employment Length                          0.0                           0.0\n", "Policy Code                                0.0                           0.0"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([dict_post_nov5_clean_number_missing, dict_post_nov5_clean_percentage_missing], index=[\"Number of missing values\", \"Percentage of missing values\"]).T"]}, {"cell_type": "code", "execution_count": 58, "id": "e4b6a1fd", "metadata": {}, "outputs": [], "source": ["SAVE_PATH =pl.Path.cwd() / \"data\" / \"Full Lending Club loan data\" / \"rejected_2007_to_2018q4\" / \"rejected_clean.csv\"\n", "df_post_nov5_clean.to_csv(SAVE_PATH, index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}