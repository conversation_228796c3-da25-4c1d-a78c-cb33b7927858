"""
Pydantic models for LLM responses and evaluation results.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class LoanDecision(str, Enum):
    """Loan approval decision enumeration."""
    APPROVE = "APPROVE"
    DENY = "DENY"
    CONDITIONAL = "CONDITIONAL"  # For cases where additional information is needed


class ConfidenceLevel(str, Enum):
    """Confidence level enumeration."""
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"


class RiskLevel(str, Enum):
    """Risk assessment enumeration."""
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"


class LLMResponse(BaseModel):
    """Base model for LLM responses."""

    decision: LoanDecision = Field(...,
                                   description="The loan approval decision")
    confidence: ConfidenceLevel = Field(...,
                                        description="Confidence level in the decision")
    risk_assessment: RiskLevel = Field(...,
                                       description="Overall risk level assessment")

    # Detailed explanation
    reasoning: str = Field(...,
                           description="Detailed reasoning for the decision")
    key_factors: List[str] = Field(...,
                                   description="Key factors that influenced the decision")

    # Risk factors
    positive_factors: List[str] = Field(
        default_factory=list, description="Factors supporting approval")
    negative_factors: List[str] = Field(
        default_factory=list, description="Factors against approval")

    # Numerical assessments
    approval_probability: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Probability of approval (0-1)")
    default_probability: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Estimated probability of default")

    # Metadata
    model_name: Optional[str] = Field(
        None, description="Name of the model that generated this response")
    processing_time: Optional[float] = Field(
        None, description="Time taken to generate response in seconds")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Response generation timestamp")

    @validator('approval_probability', 'default_probability')
    def validate_probabilities(cls, v):
        """Ensure probabilities are between 0 and 1."""
        if v is not None and not (0.0 <= v <= 1.0):
            raise ValueError("Probabilities must be between 0.0 and 1.0")
        return v

    def to_json_serializable(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary."""
        return {
            "decision": self.decision.value,
            "confidence": self.confidence.value,
            "risk_assessment": self.risk_assessment.value,
            "reasoning": self.reasoning,
            "key_factors": self.key_factors,
            "positive_factors": self.positive_factors,
            "negative_factors": self.negative_factors,
            "approval_probability": self.approval_probability,
            "default_probability": self.default_probability,
            "model_name": self.model_name,
            "processing_time": self.processing_time,
            "timestamp": self.timestamp.isoformat()
        }


class SingleModelResponse(LLMResponse):
    """Response from a single frontier model."""

    approach: Literal["single_frontier"] = "single_frontier"

    # Additional fields specific to single model
    temperature_used: Optional[float] = Field(
        None, description="Temperature parameter used")
    max_tokens_used: Optional[int] = Field(
        None, description="Max tokens parameter used")


class PoLLResponse(LLMResponse):
    """Response from Plateau of LLMs approach."""

    approach: Literal["poll"] = "poll"

    # Individual model responses
    individual_responses: List[Dict[str, Any]] = Field(
        default_factory=list, description="Responses from individual models")
    ensemble_method: str = Field(...,
                                 description="Method used to combine responses")
    model_weights: Optional[Dict[str, float]] = Field(
        None, description="Weights assigned to each model")

    # Consensus metrics
    consensus_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Agreement level between models")
    disagreement_factors: List[str] = Field(
        default_factory=list, description="Factors causing disagreement")

    # Enhanced analysis for binary decisions
    individual_model_analyses: List[Dict[str, Any]] = Field(
        default_factory=list, description="Detailed analysis from each model")


class CourtSystemResponse(LLMResponse):
    """Response from Court System approach."""

    approach: Literal["court_system"] = "court_system"

    # Individual court responses
    pros_analysis: str = Field(..., description="Analysis of positive factors")
    cons_analysis: str = Field(..., description="Analysis of negative factors")
    judge_reasoning: str = Field(..., description="Final judge's reasoning")

    # Court-specific metadata
    pros_model: Optional[str] = Field(
        None, description="Model used for pros analysis")
    cons_model: Optional[str] = Field(
        None, description="Model used for cons analysis")
    judge_model: Optional[str] = Field(
        None, description="Model used for final judgment")

    # Deliberation metrics
    pros_strength: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Strength of positive arguments")
    cons_strength: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Strength of negative arguments")


class EvaluationResult(BaseModel):
    """Model for evaluation results comparing predictions to ground truth."""

    # Prediction details
    predicted_decision: LoanDecision = Field(...,
                                             description="LLM predicted decision")
    actual_outcome: bool = Field(...,
                                 description="Actual loan outcome (True=good, False=bad)")

    # Correctness
    is_correct: bool = Field(...,
                             description="Whether the prediction was correct")
    prediction_type: Literal["TP", "TN", "FP",
                             "FN"] = Field(..., description="Prediction classification")

    # Confidence and risk alignment
    confidence_level: ConfidenceLevel = Field(
        ..., description="LLM confidence level")
    risk_level: RiskLevel = Field(..., description="LLM risk assessment")

    # Metadata
    loan_id: Optional[str] = Field(None, description="Unique loan identifier")
    approach_used: str = Field(...,
                               description="LLM approach used for prediction")
    evaluation_timestamp: datetime = Field(
        default_factory=datetime.now, description="Evaluation timestamp")

    @validator('prediction_type')
    def validate_prediction_type(cls, v, values):
        """Validate prediction type based on predicted and actual values."""
        # Make sure predicted_decision and actual_outcome are present and validated in the EvaluationResult model
        if 'predicted_decision' in values and 'actual_outcome' in values:
            # the or
            predicted_approve: bool = values['predicted_decision'] == LoanDecision.APPROVE
            actual_good = values['actual_outcome']

            if predicted_approve and actual_good:
                expected = "TP"
            elif not predicted_approve and not actual_good:
                expected = "TN"
            elif predicted_approve and not actual_good:
                expected = "FP"
            else:  # not predicted_approve and actual_good
                expected = "FN"

            if v != expected:
                raise ValueError(
                    f"Prediction type {v} doesn't match expected {expected}")

        return v


class BatchEvaluationResult(BaseModel):
    """Model for batch evaluation results."""

    # Overall metrics
    total_predictions: int = Field(...,
                                   description="Total number of predictions")
    correct_predictions: int = Field(...,
                                     description="Number of correct predictions")
    accuracy: float = Field(..., ge=0.0, le=1.0,
                            description="Overall accuracy")

    # Confusion matrix
    true_positives: int = Field(..., description="True positives")
    true_negatives: int = Field(..., description="True negatives")
    false_positives: int = Field(..., description="False positives")
    false_negatives: int = Field(..., description="False negatives")

    # Derived metrics
    precision: float = Field(..., ge=0.0, le=1.0,
                             description="Precision score")
    recall: float = Field(..., ge=0.0, le=1.0, description="Recall score")
    f1_score: float = Field(..., ge=0.0, le=1.0, description="F1 score")

    # Approach-specific results
    approach_name: str = Field(..., description="Name of the LLM approach")
    individual_results: List[EvaluationResult] = Field(
        ..., description="Individual evaluation results")

    # Processing metadata
    evaluation_duration: float = Field(...,
                                       description="Time taken for evaluation in seconds")
    total_llm_processing_time: float = Field(
        0.0, description="Total time spent on LLM processing in seconds")
    batch_id: Optional[str] = Field(None, description="Batch identifier")
    evaluation_timestamp: datetime = Field(
        default_factory=datetime.now, description="Evaluation timestamp")

    @validator('accuracy')
    def validate_accuracy(cls, v, values):
        """Validate accuracy calculation."""
        if 'correct_predictions' in values and 'total_predictions' in values:
            expected_accuracy = values['correct_predictions'] / \
                values['total_predictions']
            if abs(v - expected_accuracy) > 0.001:  # Allow small floating point differences
                raise ValueError(
                    f"Accuracy {v} doesn't match calculated {expected_accuracy}")
        return v

    def get_confusion_matrix(self) -> Dict[str, int]:
        """Get confusion matrix as dictionary."""
        return {
            "TP": self.true_positives,
            "TN": self.true_negatives,
            "FP": self.false_positives,
            "FN": self.false_negatives
        }

    def get_summary_stats(self) -> Dict[str, float]:
        """Get summary statistics."""
        return {
            "accuracy": self.accuracy,
            "precision": self.precision,
            "recall": self.recall,
            "f1_score": self.f1_score,
            "total_predictions": self.total_predictions,
            "correct_predictions": self.correct_predictions
        }


class ComparisonResult(BaseModel):
    """Model for comparing different LLM approaches."""

    approach_results: Dict[str, BatchEvaluationResult] = Field(
        ..., description="Results for each approach")
    best_approach: str = Field(...,
                               description="Name of the best performing approach")
    best_metric: str = Field(...,
                             description="Metric used to determine best approach")

    # Comparative metrics
    accuracy_ranking: List[str] = Field(...,
                                        description="Approaches ranked by accuracy")
    precision_ranking: List[str] = Field(...,
                                         description="Approaches ranked by precision")
    recall_ranking: List[str] = Field(...,
                                      description="Approaches ranked by recall")
    f1_ranking: List[str] = Field(...,
                                  description="Approaches ranked by F1 score")

    # Statistical significance
    statistical_tests: Optional[Dict[str, Any]] = Field(
        None, description="Statistical test results")

    comparison_timestamp: datetime = Field(
        default_factory=datetime.now, description="Comparison timestamp")

    def get_performance_summary(self) -> Dict[str, Dict[str, float]]:
        """Get performance summary for all approaches."""
        return {
            approach: result.get_summary_stats()
            for approach, result in self.approach_results.items()
        }
